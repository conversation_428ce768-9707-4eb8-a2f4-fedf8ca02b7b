import os
import sys
import platform
import subprocess
import venv
from pathlib import Path


def is_virtual_env():
    """检查是否在虚拟环境中运行"""
    return sys.prefix != sys.base_prefix


def detect_os():
    """检测操作系统类型"""
    return platform.system()


def has_nvidia_gpu():
    """检测是否有NVIDIA GPU可用"""
    try:
        # 尝试运行nvidia-smi命令
        subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        return True
    except (subprocess.SubprocessError, FileNotFoundError):
        # 如果命令失败或不存在，则检查其他方法
        os_type = detect_os()

        if os_type == 'Windows':
            # Windows系统下使用WMI查询
            try:
                import wmi
                wmi_obj = wmi.WMI()
                for gpu in wmi_obj.Win32_VideoController():
                    if 'nvidia' in gpu.Name.lower():
                        return True
            except ImportError:
                pass
        elif os_type == 'Darwin':  # macOS
            # macOS下通过system_profiler检查
            try:
                result = subprocess.run(['system_profiler', 'SPDisplaysDataType'],
                                        stdout=subprocess.PIPE, text=True)
                return 'NVIDIA' in result.stdout
            except subprocess.SubprocessError:
                pass

        return False


def create_venv_and_restart():
    """创建虚拟环境并重启脚本"""
    venv_path = Path('.venv')

    print("未检测到虚拟环境，将创建新的虚拟环境...")
    venv.create(venv_path, with_pip=True)

    # 根据操作系统确定python可执行文件路径
    os_type = detect_os()
    if os_type == 'Windows':
        python_exe = venv_path / 'Scripts' / 'python.exe'
    else:  # Unix-like系统
        python_exe = venv_path / 'bin' / 'python'

    print(f"虚拟环境创建完成，现在将在虚拟环境中重启脚本...")

    # 使用新的Python解释器运行当前脚本
    script_path = Path(__file__).resolve()
    os.execl(str(python_exe), str(python_exe), str(script_path))


def is_package_installed(package_name):
    """检查指定的包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False


def check_installed_packages():
    """检查必要的包是否已安装"""
    print("检查已安装的包...")

    # 检查核心包
    packages_status = {
        'torch': is_package_installed('torch'),
        'torch_geometric': is_package_installed('torch_geometric')
    }

    # 打印检查结果
    for package, installed in packages_status.items():
        status = "已安装" if installed else "未安装"
        print(f"{package}: {status}")

    return packages_status


def set_pip_mirror():
    """设置pip源为清华源"""
    print("正在将pip源设置为清华源...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'config', 'set', 'global.index-url',
                               'https://pypi.tuna.tsinghua.edu.cn/simple'])
        print("pip源已成功设置为清华源")
    except subprocess.SubprocessError as e:
        print(f"设置pip源时出错: {e}")
        print("将在单个命令中使用清华源")


def install_packages():
    """根据环境安装PyTorch和依赖项"""
    # 设置pip源为清华源
    set_pip_mirror()

    # 首先检查已安装的包
    packages_status = check_installed_packages()

    os_type = detect_os()
    has_gpu = has_nvidia_gpu()

    print(f"检测到操作系统: {os_type}")
    print(f"GPU可用性: {'可用' if has_gpu else '不可用'}")

    # 安装基本依赖
    print("安装基本依赖...")
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-U', 'pip'])
    basic_packages = [
        'matplotlib',
        'tqdm',
        'scikit-learn',
        'pandas'
    ]
    subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + basic_packages)

    # 仅在PyTorch未安装时安装
    if not packages_status['torch']:
        print("安装PyTorch...")
        if os_type == 'Windows':
            if has_gpu:
                # Windows with CUDA
                torch_command = [sys.executable, '-m', 'pip', 'install', 'torch', 'torchvision', 'torchaudio',
                                 '--index-url', 'https://mirror.nju.edu.cn/pytorch/whl/cu118']
            else:
                # Windows CPU only
                torch_command = [sys.executable, '-m', 'pip', 'install', 'torch', 'torchvision', 'torchaudio']
        elif os_type == 'Darwin':  # macOS
            if platform.processor() == 'arm':
                # M1/M2 Mac
                torch_command = [sys.executable, '-m', 'pip', 'install', 'torch', 'torchvision', 'torchaudio']
            else:
                # Intel Mac
                torch_command = [sys.executable, '-m', 'pip', 'install', 'torch', 'torchvision', 'torchaudio']
        else:  # Linux
            if has_gpu:
                # Linux with CUDA
                torch_command = [sys.executable, '-m', 'pip', 'install', 'torch', 'torchvision', 'torchaudio',
                                 '--index-url', 'https://mirror.nju.edu.cn/pytorch/whl/cu118']
            else:
                # Linux CPU only
                torch_command = [sys.executable, '-m', 'pip', 'install', 'torch', 'torchvision', 'torchaudio',
                                 '--index-url', 'https://mirror.nju.edu.cn/pytorch/whl/cpu']

        # 执行PyTorch安装命令
        subprocess.check_call(torch_command)
    else:
        print("PyTorch已安装，跳过安装步骤")

    # 仅在PyG未安装时安装
    if not packages_status['torch_geometric']:
        print("安装图神经网络相关依赖...")
        gnn_packages = [
            'torch-geometric'
        ]
        subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + gnn_packages)
    else:
        print("PyTorch Geometric已安装，跳过安装步骤")

    print("所有依赖检查/安装完成！")


def verify_installation():
    """验证PyTorch安装情况"""
    print("\n验证PyTorch安装...")
    verification_code = """
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA是否可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"GPU数量: {torch.cuda.device_count()}")
    for i in range(torch.cuda.device_count()):
        print(f"GPU {i}: {torch.cuda.get_device_name(i)}")

try:
    import torch_geometric
    print(f"\\nPyTorch Geometric版本: {torch_geometric.__version__}")
except ImportError:
    print("PyTorch Geometric未安装")
"""
    subprocess.run([sys.executable, '-c', verification_code])


def main():
    print("开始环境检测和PyTorch安装流程...")

    # 检查是否在虚拟环境中运行
    if not is_virtual_env():
        create_venv_and_restart()
    else:
        print("检测到虚拟环境，继续安装...")

    # 安装所需包
    install_packages()

    # 验证安装
    verify_installation()

    print("\n安装流程完成！现在可以使用PyTorch和其他相关库了。")


if __name__ == "__main__":
    main()
