<!DOCTYPE html>
<!-- saved from url=(0058)https://www.datawhale.cn/activity/354/learn/200/4430/51/43 -->
<html lang="zh"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <link href="https://datawhale.oss-cn-hangzhou.aliyuncs.com/SEO/datawhale-logo.png" rel="shortcut icon">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 不要删除以下google和baidu两个meta！！ -->
    <meta name="google-site-verification" content="Xg_pk0z2O2RvUhWVlxf32pN2oREpYSlQigyG_C5XYps">
    <meta name="baidu-site-verification" content="codeva-8XGXfChz73">
    <meta name="description" content="小白友好，线上活动，全程免费。">
    <!-- 因为手机浏览器中的聚焦输入框会触发浏览器的缩放功能，以便用户更清楚地看到输入框里的内容并进行输入。
      所以通过设置meta标签来限制页面的放大缩小，设置 minimum-scale=1.0, maximum-scale=1.0，限制页面只能1.0倍
    -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="keywords" content="Datawhale,科鲸,组队学习,高校行,AI学习,AI夏令营,开源学习,人工智能,Datawhale社区">
    <link rel="stylesheet" href="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/katex.min.css">
    <title>魔搭Agent赛事（Agent端侧开发） | Datawhale</title>
    <script charset="utf-8" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/UrlChangeTracker.js.下载"></script><script src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/hm.js.下载"></script><script>
      /* 百度统计代码 */
      var _hmt = _hmt || [];
      (function () {
        var hm = document.createElement('script');
        hm.src = 'https://hm.baidu.com/hm.js?afc2176cca9216b461acf4fd7afd9fe1';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(hm, s);
        /* 下面的代码非常重要！！！ 文档：https://tongji.baidu.com/web/help/article?id=324&type=0 */
        _hmt.push([
          '_requirePlugin',
          'UrlChangeTracker',
          {
            shouldTrackUrlChange: function (newPath, oldPath) {
              // 只在正式服发送日志
              return newPath && oldPath && window.location.href.startsWith('https://www.datawhale.cn');
            },
          },
        ]);
      })();
    </script>
    <script type="module" crossorigin="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/index-uHqYZEPo.js.下载"></script>
    <link rel="stylesheet" crossorigin="" href="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/index-BjM40Nni.css">
  <link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/Base-hOouZvGU.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/index-ehvUVWiJ.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/function-call-Cq_3802z.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/use-touch-wNGpouuQ.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/index-fkr-Ge5L.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/use-route-CiUReFMU.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/index-Bk0vyTA4.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/use-id-B9n94Lmr.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/index-CGvSpMPO.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/function-call-ixUX1Rpz.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/Dialog-CMbVAt2F.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/index-Ce6rqKFP.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/index-BOtQXuE_.js"><link rel="stylesheet" href="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/Base-QL8poW2v.css"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/ActivityLearnContent-D1UkQ6xU.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/learn-BUrrSCRg.js"><link rel="stylesheet" href="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/learn-BnDtFEOW.css"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/activity-MWHu37nO.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/learn-D4eBTCLb.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/index-BJ8LFxT9.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/DetailsComments-CQrHv7Up.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/function-call-CeJy7ZxO.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/index-CjyOwdUi.js"><link rel="stylesheet" href="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/DetailsComments-BhuzqSJo.css"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/activityLearnStore-B-H64L7t.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/FormDetail-DwRAYH-q.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/utils-CWrbHAtD.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/index-DtCGQn0c.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/404-Dz5-XVDG.js"><link rel="stylesheet" href="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/FormDetail-DP4ENKRX.css"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/style-BpO1Dd2C.js"><link rel="stylesheet" href="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/style-Bsa5x87W.css"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/wst-render-index-Dyj0Mofe-ppK4F5Jm.js"><link rel="stylesheet" href="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/ActivityLearnContent-_gwdadDi.css"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/Doc-6s4VMDsx-CEGc5yyZ.js"><link rel="modulepreload" as="script" crossorigin="" href="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/index-uHqYZEPo.js.下载"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/Blockquote-BamWUQND-TIIA1kWD.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/Heading-seWeR5SJ-R1aXKYWy.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/Paragraph-C7uPejOv-BhU_j9R8.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/BulletList-BQTUD69X-T4OVudkE.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/OrderedList-Dg4I3FYd-CUQeMX8W.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/Image-DG1udz5e-t28rpdpw.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/AttachmentBlock-Bh31dCsh-Cuhq6uwk.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/AttachmentMenu-DD__NbKU-C9JLmapY.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/CodeBlock-D36tIGwV-DB3ME2LM.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/FoldBlock-BfWS_HKv-8WLG1ufE.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/emoji-CmOToDEU-DT7KHLhU.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/TableContainer-C9PhNGlz-D91I7TC5.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/Grid-Cw7GnpWk-DvEV0nby.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/wechat-BI_6pBEp.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/Text-sfAat5bz-DBU7QA2k.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/SlotNode-Ambnn0IX-BLzdGMai.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/ListItem-X0T7a_z8-Cl8tD_lr.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/TableRow-OzpVq1qW-ScdznsyS.js"><link rel="modulepreload" as="script" crossorigin="" href="https://www.datawhale.cn/assets/TableCell-QmpT9gKY-CvyEz0lC.js"></head>
  <body>
    <div id="app" data-v-app=""><div data-v-838a899c="" class="container"><!----><div data-v-74d9ef0f="" data-v-838a899c="" class="container router-view-learn-content"><div data-v-74d9ef0f="" class="learn-content-header"><div data-v-74d9ef0f="" class="header"><!----><!----><div data-v-74d9ef0f="" class="icons"><img data-v-74d9ef0f="" src="data:image/svg+xml,%3csvg%20xmlns=&#39;http://www.w3.org/2000/svg&#39;%20xmlns:xlink=&#39;http://www.w3.org/1999/xlink&#39;%20viewBox=&#39;0%200%2032%2032&#39;%20width=&#39;30&#39;%20height=&#39;24&#39;%20style=&#39;&#39;%20filter=&#39;none&#39;%3e%3cg%3e%3cpath%20d=&#39;M9.476%2016.257c-0.103-0.435%200.015-0.912%200.355-1.251l10.685-10.685c0.521-0.521%201.365-0.521%201.886%200s0.521%201.365%200%201.886l-9.745%209.745%209.746%209.746c0.521%200.521%200.521%201.365%200%201.886s-1.365%200.521-1.886%200l-10.685-10.685c-0.171-0.171-0.296-0.388-0.354-0.632l-0.002-0.009z&#39;%20fill=&#39;rgba(51,51,51,1)&#39;%3e%3c/path%3e%3c/g%3e%3c/svg%3e"><img data-v-74d9ef0f="" src="data:image/svg+xml,%3csvg%20xmlns=&#39;http://www.w3.org/2000/svg&#39;%20xmlns:xlink=&#39;http://www.w3.org/1999/xlink&#39;%20viewBox=&#39;0%200%2048%2048&#39;%20width=&#39;24&#39;%20height=&#39;24&#39;%20style=&#39;&#39;%20filter=&#39;none&#39;%3e%3cg%3e%3cpath%20d=&#39;M9%2042C11.2091%2042%2013%2040.2091%2013%2038C13%2035.7909%2011.2091%2034%209%2034C6.79086%2034%205%2035.7909%205%2038C5%2040.2091%206.79086%2042%209%2042Z&#39;%20fill=&#39;none&#39;%20stroke=&#39;rgba(16,16,16,1)&#39;%20stroke-width=&#39;4&#39;%20stroke-linejoin=&#39;round&#39;%3e%3c/path%3e%3cpath%20d=&#39;M9%2014C11.2091%2014%2013%2012.2092%2013%2010C13%207.79086%2011.2091%206%209%206C6.79086%206%205%207.79086%205%2010C5%2012.2092%206.79086%2014%209%2014Z&#39;%20fill=&#39;none&#39;%20stroke=&#39;rgba(16,16,16,1)&#39;%20stroke-width=&#39;4&#39;%20stroke-linejoin=&#39;round&#39;%3e%3c/path%3e%3cpath%20d=&#39;M9%2028C11.2091%2028%2013%2026.2092%2013%2024C13%2021.7908%2011.2091%2020%209%2020C6.79086%2020%205%2021.7908%205%2024C5%2026.2092%206.79086%2028%209%2028Z&#39;%20fill=&#39;none&#39;%20stroke=&#39;rgba(16,16,16,1)&#39;%20stroke-width=&#39;4&#39;%20stroke-linejoin=&#39;round&#39;%3e%3c/path%3e%3cpath%20d=&#39;M21%2024H43&#39;%20stroke=&#39;rgba(16,16,16,1)&#39;%20stroke-width=&#39;4&#39;%20stroke-linecap=&#39;round&#39;%20stroke-linejoin=&#39;round&#39;%20fill=&#39;none&#39;%3e%3c/path%3e%3cpath%20d=&#39;M21%2038H43&#39;%20stroke=&#39;rgba(16,16,16,1)&#39;%20stroke-width=&#39;4&#39;%20stroke-linecap=&#39;round&#39;%20stroke-linejoin=&#39;round&#39;%20fill=&#39;none&#39;%3e%3c/path%3e%3cpath%20d=&#39;M21%2010H43&#39;%20stroke=&#39;rgba(16,16,16,1)&#39;%20stroke-width=&#39;4&#39;%20stroke-linecap=&#39;round&#39;%20stroke-linejoin=&#39;round&#39;%20fill=&#39;none&#39;%3e%3c/path%3e%3c/g%3e%3c/svg%3e"></div><div data-v-74d9ef0f="" class="header-title"><span data-v-74d9ef0f="">夏令营：开发端侧Agent应用</span></div><div data-v-74d9ef0f="" class="header-right"><div data-v-74d9ef0f="" class="header-right-avatar-wrapper"><span class="van-popover__wrapper"><img data-v-74d9ef0f="" class="header-right-avatar" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/132" alt="avatar"></span></div></div></div></div><div data-v-74d9ef0f="" class="content"><div data-v-74d9ef0f="" class="mobile-content-index"><div data-v-74d9ef0f="" class="tree-render"><div class="doc"><blockquote><p data-v-550653ec="" class="paragraph">赛事链接：<a target="https://startup.aliyun.com/aihackathon/mcp-agent" href="https://startup.aliyun.com/aihackathon/mcp-agent" class="" rel="noopener noreferrer nofollow">https://startup.aliyun.com/aihackathon/mcp-agent</a></p></blockquote><h2 class="heading">一、背景介绍</h2><p data-v-550653ec="" class="paragraph">随着人工智能技术的快速发展，端侧智能体（Edge AI Agent）正成为AI应用的重要趋势。端侧部署不仅能够保护用户隐私，降低云端计算成本，还能提供更快的响应速度和更好的用户体验。</p><p data-v-550653ec="" class="paragraph"></p><p data-v-550653ec="" class="paragraph">在AI技术快速发展的今天，我们面临一个重要问题： <strong>如何在保证数据隐私和安全的前提下，构建一个高效、可控的端侧智能体系统？</strong></p><p data-v-550653ec="" class="paragraph">想象这样的场景：</p><ul><li><p data-v-550653ec="" class="paragraph">企业需要AI助手处理敏感文档，但不希望数据上传到云端</p></li><li><p data-v-550653ec="" class="paragraph">个人用户希望拥有一个完全私有的AI伴侣，能够理解自己的喜好和习惯，分享私密信息</p></li><li><p data-v-550653ec="" class="paragraph">开发者需要一个可以离线运行、快速响应的AI工具来提升工作效率</p></li></ul><p data-v-550653ec="" class="paragraph">这些需求都指向同一个方向： <strong>端侧智能体</strong> 。但如何从零开始构建这样一个系统？并实现交互达到可用？这也是本赛题的实现目的。</p><p data-v-550653ec="" class="paragraph"></p><h2 class="heading"><strong>二、赛题解读</strong></h2><h3 class="heading"><strong>2.1 赛题分析</strong></h3><p data-v-550653ec="" class="paragraph">Agent端侧智能体这个比赛，核心在于回答一个关键问题：Deepseek-R1、阿里Qwen3、谷歌Gemini等大模型提供的API服务能满足大部分需求， <strong>为什么还要本地agent侧微调小模型，意义在哪？</strong></p><p data-v-550653ec="" class="paragraph">通过该赛题引导出了三个大模型的现实问题：</p><ol start="1"><li><p data-v-550653ec="" class="paragraph"><strong>数据安全隐私</strong> ：公司的敏感数据是否需要保密？当处理电信投诉、银行数据、医疗病历、法律申诉等敏感信息时，直接调用外部API就存在数据泄露风险，即使大模型供应商保证不会拿数据做训练，但数据传输本身就是一种泄露；</p></li><li><p data-v-550653ec="" class="paragraph"><strong>成本控制与实际需求</strong> ：如果实际需求是做信息抽取，对几百万条数据部署大模型，可能要消耗几万块。但如果只用小模型，可以大幅降低成本来完成这个需求任务；</p></li><li><p data-v-550653ec="" class="paragraph"><strong>端侧部署能力的趋势</strong> ：模型必须能在本地设备运行，而不依赖云端服务，这是AI应用的发展方向。当每个人都能拥有自己的专属AI助手完全在本地运行，不会泄露任何隐私信息，在特定环境下为用户提供帮助时，我们就实现了 AI的实用化落地 。这就是端侧智能体的使命，也是这个竞赛想要推动的未来。</p></li></ol><p data-v-550653ec="" class="paragraph"></p><h3 class="heading"><strong>2.2 赛题需求</strong></h3><p data-v-550653ec="" class="paragraph">这个竞赛实际上是在寻找 <strong>如何用小模型解决特定领域问题的最佳实践</strong> ，它要求我们在 <strong>资源受限的端侧环境</strong> 中，通过 <strong>微调小模型</strong> 来实现特定任务的高效处理。比如微调一个Qwen3-0.6B小模型，完成隐私数据的简单信息抽取。既可以保证模型抽取效果，又能保证数据不泄露，而且，还可以省钱！</p><p data-v-550653ec="" class="paragraph">完成本赛题，需要掌握 <strong>端侧训练、微调、部署模型</strong> 的能力，在有限的计算资源下，实现接近云端大模型的用户体验，完成特定的任务需求。</p><p data-v-550653ec="" class="paragraph"></p><h3 class="heading"><strong>2.3 赛题重难点</strong></h3><ol start="1"><li><p data-v-550653ec="" class="paragraph"><strong>应用场景的深度挖掘</strong></p><p data-v-550653ec="" class="paragraph">高分的关键在于找到真正有价值的应用场景。提供两个场景供参考：</p><ul><li><p data-v-550653ec="" class="paragraph"><strong>隐私保护</strong> ：当医生需要处理患者病历，律师需要分析法律文档，研究人员需要处理敏感数据时，他们需要的是一个完全可信的AI助手，而不是一个可能泄露信息的云端服务；</p></li></ul><ul><li><p data-v-550653ec="" class="paragraph"><strong>离线使用</strong> ：一个野外科考队员，在没有网络的环境下仍然能够获得AI助手的帮助；经常需要坐飞机出差的人士，能够在飞行模式下继续处理工作文档；</p></li></ul><p data-v-550653ec="" class="paragraph">这些场景的价值远远超出了技术本身。</p></li><li><p data-v-550653ec="" class="paragraph"><strong>技术创新的突破</strong></p><p data-v-550653ec="" class="paragraph">对传统方式的重新思考，比如baseline中的极简LoRA配置（r=2，只微调q_proj），不是微调多个模块，而是在特定任务上，只微调投影层，大幅降低训练成本。</p><p data-v-550653ec="" class="paragraph">当你的方式能将训练时间从60分钟降到15分钟，模型大小从20MB降到5MB时，这种改进就能体现出价值，可以快速迭代和优化自己的模型。</p></li><li><p data-v-550653ec="" class="paragraph"><strong>用户体验的产品设计</strong></p><p data-v-550653ec="" class="paragraph">优秀的用户体验不是简单的界面美化，而是对用户需求的深度理解。思考我们在什么场景下会用到这个应用，期望的不仅仅是正确的回答，更是产品使用体验。</p><p data-v-550653ec="" class="paragraph">个性化记忆功能也是用户体验的重要组成部分。当AI能够记住用户的偏好、习惯和历史对话时，每一次交互都会变得更加自然和高效。这种连续性体验是云端服务可能会丢失的，因为它需要个人数据存储和处理。</p></li><li><p data-v-550653ec="" class="paragraph"><strong>工程实现的可扩展性</strong></p><p data-v-550653ec="" class="paragraph">体现在整个系统的可维护性、可扩展性，后续有新的需求，能否在当前实现的接口上进行扩展。</p></li></ol><p data-v-550653ec="" class="paragraph"></p><h2 class="heading"><strong>三、参考方案简介</strong></h2><p data-v-550653ec="" class="paragraph">本方案基于《甄嬛传》角色数据，构建了一个完整的端侧智能体解决方案。</p><p data-v-550653ec="" class="paragraph">代码地址：<a target="https://github.com/ditingdapeng/ollama_baseline" href="https://github.com/ditingdapeng/ollama_baseline" class="" rel="noopener noreferrer nofollow">https://github.com/ditingdapeng/ollama_baseline</a></p><p data-v-550653ec="" class="paragraph"><strong>项目架构概览</strong></p><p data-v-550653ec="" class="paragraph">本项目采用模块化设计，包含以下核心组件：</p><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 707px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/a2835f59-b016-4bfb-a6b6-eb46e2162351.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph">分模型层和应用层进行实现：</p><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 451px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/05782c31-bfdb-426f-ba93-0c74f345b358.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph"></p><h2 class="heading"><strong>四、核心模块实现</strong></h2><p data-v-550653ec="" class="paragraph"></p><h3 class="heading"><strong>4.0 安装依赖</strong></h3><div data-v-4d50615b="" class="attachment-block-container"><div data-v-4d50615b="" class="attachment-block-card-container"><div data-v-4d50615b="" class="attachment-block-card-icon"><svg data-v-4d50615b="" width="48" height="48" viewBox="0 0 32 32" class="attachment-block-card-icon-svg"><defs><path d="M1.5 0h14.086a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V26.5a1.5 1.5 0 01-1.5 1.5h-19A1.5 1.5 0 010 26.5v-25A1.5 1.5 0 011.5 0z" id="icon_file_unknow_nor_svg__a"></path><path d="M16.293.293l5.414 5.414A1 1 0 0121.91 6H17.5A1.5 1.5 0 0116 4.5V.09a1 1 0 01.293.203z" id="icon_file_unknow_nor_svg__b"></path></defs><g fill="none" fill-rule="evenodd"><g transform="translate(5 2)"><use fill="#9CA3AD" xlink:href="#icon_file_unknow_nor_svg__a"></use><use fill="#646A73" xlink:href="#icon_file_unknow_nor_svg__b"></use></g><path d="M7.649 10.703h16.648V27.35H7.65z"></path><path d="M16.16 12.216c1.236 0 2.23.356 2.987 1.073.72.681 1.081 1.602 1.081 2.749a3.66 3.66 0 01-.76 2.255c-.209.266-.676.72-1.518 1.482-.42.367-.661.618-.849.937-.25.43-.371.87-.371 1.361v.549a.19.19 0 01-.19.189h-1.154a.19.19 0 01-.189-.19v-.548c0-.58.121-1.12.359-1.594.288-.618.835-1.247 1.72-2.02.56-.56.733-.74.854-.891.38-.488.566-.978.566-1.492 0-.754-.221-1.355-.645-1.796-.444-.445-1.088-.664-1.947-.664-.963 0-1.664.314-2.142.95-.378.474-.59.9-.63 1.713a9.425 9.425 0 00-.006.29.19.19 0 01-.19.188h-1.135a.19.19 0 01-.189-.191c.001-.15.003-.248.005-.296.052-1.177.411-1.923 1.08-2.702.787-.907 1.875-1.352 3.263-1.352zm-.755 12.108h1.136a.19.19 0 01.189.19v1.135a.19.19 0 01-.19.189h-1.135a.19.19 0 01-.189-.19v-1.134a.19.19 0 01.19-.19z" fill="#FFF" fill-rule="nonzero"></path></g></svg></div><div data-v-4d50615b="" class="attachment-block-card-info"><div data-v-4d50615b="" class="attachment-block-card-info-name">requirements.txt</div><div data-v-4d50615b="" class="attachment-block-card-info-size">0.53KB</div></div><div data-v-4d50615b="" class="attachment-block-card-preview"><div data-v-4d50615b="" class="attachment-block-card-preview-wrapper"><svg data-v-4d50615b="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="VisibleOutlined" class="attachment-block-card-preview-btn"><path d="M11.985 18.5c3.238 0 6.236-2.06 9.015-6.513C18.292 7.55 15.3 5.5 11.985 5.5 8.67 5.5 5.689 7.549 3 11.987c2.76 4.454 5.748 6.513 8.985 6.513ZM1.502 12.89a1.782 1.782 0 0 1 .023-1.838C4.428 6.017 7.915 3.5 11.984 3.5c4.086 0 7.594 2.538 10.523 7.614l.028.048c.296.519.294 1.16-.01 1.675-3.006 5.108-6.52 7.663-10.541 7.663-4.007 0-7.501-2.537-10.482-7.61ZM12 16a4 4 0 1 1 0-8 4 4 0 0 1 0 8Zm0-2a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" fill="rgb(100, 106, 115)"></path></svg></div></div><div data-v-4d50615b="" class="attachment-block-card-menu" style="display: none;"><div data-v-583c080e="" data-v-4d50615b="" class="attachment-menu"><ul data-v-583c080e="" class="attachment-menu-group"><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="MagnifyLeftOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M9 2a1 1 0 0 1 0 2H5.414l5.293 5.293a1 1 0 0 1-1.414 1.414L4 5.414V9a1 1 0 0 1-2 0V3a1 1 0 0 1 1-1h6Zm6 20a1 1 0 1 1 0-2h3.586l-5.293-5.293a1 1 0 0 1 1.414-1.414L20 18.586V15a1 1 0 1 1 2 0v6a1 1 0 0 1-1 1h-6Z" fill="rgb(43, 47, 54)"></path></svg></li><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="DownloadOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M20 18a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-2a1 1 0 1 1 2 0v1h14v-1a1 1 0 0 1 1-1Zm-7-3.964 2.657-2.657a1 1 0 0 1 1.414 1.414c-1.414 1.415-2.828 2.83-4.244 4.244a1 1 0 0 1-1.412 0c-1.417-1.415-2.833-2.833-4.249-4.25a.993.993 0 0 1 .013-1.401.992.992 0 0 1 1.401-.013l2.42 2.42V3.5a1 1 0 1 1 2 0v10.536Z" fill="rgb(43, 47, 54)"></path></svg></li></ul></div></div></div></div><p data-v-550653ec="" class="paragraph"></p><p data-v-550653ec="" class="paragraph"><strong>方式一：使用 pip 安装</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#6F42C1">pip</span><span style="color:#2B5581"> install</span><span style="color:#2B5581"> -r</span><span style="color:#2B5581"> requirements.txt</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><p data-v-550653ec="" class="paragraph"><strong>方式二：使用 conda 环境（推荐）</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 1. 创建conda环境（指定Python版本）</span></span>
<span class="line"><span style="color:#6F42C1">conda</span><span style="color:#2B5581"> create</span><span style="color:#2B5581"> -n</span><span style="color:#2B5581"> huanhuan</span><span style="color:#2B5581"> python=</span><span style="color:#1976D2">3.13</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 2. 激活环境</span></span>
<span class="line"><span style="color:#6F42C1">conda</span><span style="color:#2B5581"> activate</span><span style="color:#2B5581"> huanhuan</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 3. 安装依赖</span></span>
<span class="line"><span style="color:#6F42C1">pip</span><span style="color:#2B5581"> install</span><span style="color:#2B5581"> -r</span><span style="color:#2B5581"> requirements.txt</span></span>
<span class="line"><span style="color:#C2C3C5"># 或者优先使用conda安装</span></span>
<span class="line"><span style="color:#6F42C1">conda</span><span style="color:#2B5581"> install</span><span style="color:#2B5581"> pytorch</span><span style="color:#2B5581"> transformers</span><span style="color:#2B5581"> -c</span><span style="color:#2B5581"> pytorch</span><span style="color:#2B5581"> -c</span><span style="color:#2B5581"> huggingface</span></span>
<span class="line"><span style="color:#6F42C1">pip</span><span style="color:#2B5581"> install</span><span style="color:#2B5581"> -r</span><span style="color:#2B5581"> requirements.txt</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 4. 退出环境</span></span>
<span class="line"><span style="color:#6F42C1">conda</span><span style="color:#2B5581"> deactivate</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><p data-v-550653ec="" class="paragraph"><strong>方式三：使用 uv（推荐）</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 1. 安装uv（如果未安装）</span></span>
<span class="line"><span style="color:#6F42C1">pip</span><span style="color:#2B5581"> install</span><span style="color:#2B5581"> uv</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 2. 创建虚拟环境</span></span>
<span class="line"><span style="color:#6F42C1">uv</span><span style="color:#2B5581"> venv</span><span style="color:#2B5581"> huanhuan_env</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 3. 激活环境</span></span>
<span class="line"><span style="color:#6F42C1">source</span><span style="color:#2B5581"> huanhuan_env/bin/activate</span><span style="color:#C2C3C5">  # macOS/Linux</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 4. 使用uv安装依赖（比pip快10-100倍）</span></span>
<span class="line"><span style="color:#6F42C1">uv</span><span style="color:#2B5581"> pip</span><span style="color:#2B5581"> install</span><span style="color:#2B5581"> -r</span><span style="color:#2B5581"> requirements.txt</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><h3 class="heading"><strong>4.1 数据下载模块</strong></h3><div data-v-4d50615b="" class="attachment-block-container"><div data-v-4d50615b="" class="attachment-block-card-container"><div data-v-4d50615b="" class="attachment-block-card-icon"><svg data-v-4d50615b="" width="48" height="48" viewBox="0 0 32 32" class="attachment-block-card-icon-svg"><defs><path d="M1.5 0h14.086a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V26.5a1.5 1.5 0 01-1.5 1.5h-19A1.5 1.5 0 010 26.5v-25A1.5 1.5 0 011.5 0z" id="icon_file_js_svg__a"></path><path d="M16.293.293l5.414 5.414A1 1 0 0121.91 6H17.5A1.5 1.5 0 0116 4.5V.09a1 1 0 01.293.203z" id="icon_file_js_svg__b"></path></defs><g fill="none" fill-rule="evenodd"><g transform="translate(5 2)"><use fill="#eef" xlink:href="#icon_file_js_svg__a"></use><use fill="#ccc" xlink:href="#icon_file_js_svg__b"></use></g><g transform="translate(7 7) scale(0.019)"><path d="M420.693333 85.333333C353.28 85.333333 298.666667 139.946667 298.666667 207.36v71.68h183.04c16.64 0 30.293333 24.32 30.293333 40.96H207.36C139.946667 320 85.333333 374.613333 85.333333 442.026667v161.322666c0 67.413333 54.613333 122.026667 122.026667 122.026667h50.346667v-114.346667c0-67.413333 54.186667-122.026667 121.6-122.026666h224c67.413333 0 122.026667-54.229333 122.026666-121.642667V207.36C725.333333 139.946667 670.72 85.333333 603.306667 85.333333z m-30.72 68.693334c17.066667 0 30.72 5.12 30.72 30.293333s-13.653333 38.016-30.72 38.016c-16.64 0-30.293333-12.8-30.293333-37.973333s13.653333-30.336 30.293333-30.336z" fill="#3C78AA"></path><path d="M766.250667 298.666667v114.346666a121.6 121.6 0 0 1-121.6 121.984H420.693333A121.6 121.6 0 0 0 298.666667 656.597333v160a122.026667 122.026667 0 0 0 122.026666 122.026667h182.613334A122.026667 122.026667 0 0 0 725.333333 816.64v-71.68h-183.082666c-16.64 0-30.250667-24.32-30.250667-40.96h304.64A122.026667 122.026667 0 0 0 938.666667 581.973333v-161.28a122.026667 122.026667 0 0 0-122.026667-122.026666zM354.986667 491.221333l-0.170667 0.170667c0.512-0.085333 1.066667-0.042667 1.621333-0.170667z m279.04 310.442667c16.64 0 30.293333 12.8 30.293333 37.973333a30.293333 30.293333 0 0 1-30.293333 30.293334c-17.066667 0-30.72-5.12-30.72-30.293334s13.653333-37.973333 30.72-37.973333z" fill="#FDD835"></path></g></g></svg></div><div data-v-4d50615b="" class="attachment-block-card-info"><div data-v-4d50615b="" class="attachment-block-card-info-name">download_data.py</div><div data-v-4d50615b="" class="attachment-block-card-info-size">2.63KB</div></div><div data-v-4d50615b="" class="attachment-block-card-preview"><div data-v-4d50615b="" class="attachment-block-card-preview-wrapper"><svg data-v-4d50615b="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="VisibleOutlined" class="attachment-block-card-preview-btn"><path d="M11.985 18.5c3.238 0 6.236-2.06 9.015-6.513C18.292 7.55 15.3 5.5 11.985 5.5 8.67 5.5 5.689 7.549 3 11.987c2.76 4.454 5.748 6.513 8.985 6.513ZM1.502 12.89a1.782 1.782 0 0 1 .023-1.838C4.428 6.017 7.915 3.5 11.984 3.5c4.086 0 7.594 2.538 10.523 7.614l.028.048c.296.519.294 1.16-.01 1.675-3.006 5.108-6.52 7.663-10.541 7.663-4.007 0-7.501-2.537-10.482-7.61ZM12 16a4 4 0 1 1 0-8 4 4 0 0 1 0 8Zm0-2a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" fill="rgb(100, 106, 115)"></path></svg></div></div><div data-v-4d50615b="" class="attachment-block-card-menu" style="display: none;"><div data-v-583c080e="" data-v-4d50615b="" class="attachment-menu"><ul data-v-583c080e="" class="attachment-menu-group"><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="MagnifyLeftOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M9 2a1 1 0 0 1 0 2H5.414l5.293 5.293a1 1 0 0 1-1.414 1.414L4 5.414V9a1 1 0 0 1-2 0V3a1 1 0 0 1 1-1h6Zm6 20a1 1 0 1 1 0-2h3.586l-5.293-5.293a1 1 0 0 1 1.414-1.414L20 18.586V15a1 1 0 1 1 2 0v6a1 1 0 0 1-1 1h-6Z" fill="rgb(43, 47, 54)"></path></svg></li><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="DownloadOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M20 18a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-2a1 1 0 1 1 2 0v1h14v-1a1 1 0 0 1 1-1Zm-7-3.964 2.657-2.657a1 1 0 0 1 1.414 1.414c-1.414 1.415-2.828 2.83-4.244 4.244a1 1 0 0 1-1.412 0c-1.417-1.415-2.833-2.833-4.249-4.25a.993.993 0 0 1 .013-1.401.992.992 0 0 1 1.401-.013l2.42 2.42V3.5a1 1 0 1 1 2 0v10.536Z" fill="rgb(43, 47, 54)"></path></svg></li></ul></div></div></div></div><blockquote><p data-v-550653ec="" class="paragraph"><strong>文件位置</strong> : dataScripts/download_data.py</p></blockquote><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.1.1 解决问题</strong></h4><p data-v-550653ec="" class="paragraph">数据下载模块负责从GitHub获取甄嬛传角色对话数据集，为后续的数据预处理和模型训练提供原始数据。</p><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.1.2 实现流程</strong></h4><p data-v-550653ec="" class="paragraph">下载模块的实现比较简单，解决的问题就是把目标数据下载到本地的指定位置。</p><ol start="1"><li><p data-v-550653ec="" class="paragraph">首先，因为下载器有配置信息（状态）和下载动作（行为），所以我们可以通过 <strong>类</strong> 来定义。</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">class</span><span style="color:#6F42C1"> HuanHuanDataDownloader</span><span style="color:#24292EFF">:</span></span>
<span class="line"><span style="color:#D32F2F">  def</span><span style="color:#6F42C1"> __init__</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#D32F2F">    pass</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><ol start="2"><li><p data-v-550653ec="" class="paragraph">考虑到下载到本地的路径可能不存在，那就在init初始化时设置下载配置，如果没有则完成创建。</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> __init__</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#212121">,</span><span style="color:#FF9800"> data_dir</span><span style="color:#212121">:</span><span style="color:#1976D2"> str</span><span style="color:#D32F2F"> =</span><span style="color:#22863A"> "../data"</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#24292EFF">  self</span><span style="color:#212121">.</span><span style="color:#24292EFF">data_dir </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> Path</span><span style="color:#212121">(data_dir)</span></span>
<span class="line"><span style="color:#24292EFF">  self</span><span style="color:#212121">.</span><span style="color:#24292EFF">raw_dir </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> self</span><span style="color:#212121">.</span><span style="color:#24292EFF">data_dir </span><span style="color:#D32F2F">/</span><span style="color:#22863A"> "raw"</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5">  # 创建目录</span></span>
<span class="line"><span style="color:#24292EFF">  self</span><span style="color:#212121">.</span><span style="color:#24292EFF">raw_dir</span><span style="color:#212121">.</span><span style="color:#6F42C1">mkdir</span><span style="color:#212121">(parents</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">, exist_ok</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5">  # 数据源URL</span></span>
<span class="line"><span style="color:#24292EFF">  self</span><span style="color:#212121">.</span><span style="color:#24292EFF">base_url </span><span style="color:#D32F2F">=</span><span style="color:#22863A"> "https://raw.githubusercontent.com/datawhalechina/self-llm/master/dataset"</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5">  # 数据文件</span></span>
<span class="line"><span style="color:#24292EFF">  self</span><span style="color:#212121">.</span><span style="color:#24292EFF">data_file </span><span style="color:#D32F2F">=</span><span style="color:#22863A"> "huanhuan.json"</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><ol start="3"><li><p data-v-550653ec="" class="paragraph">下载文件，通过传入目标路径和保存路径，来完成下载。考虑两部分：</p></li></ol><p data-v-550653ec="" class="paragraph">一是要分多次下载，而不能一次性，避免大文件的内存问题。</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> download_file</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#212121">,</span><span style="color:#FF9800"> url</span><span style="color:#212121">:</span><span style="color:#1976D2"> str</span><span style="color:#212121">,</span><span style="color:#FF9800"> save_path</span><span style="color:#212121">:</span><span style="color:#24292EFF"> Path</span><span style="color:#212121">,</span><span style="color:#FF9800"> description</span><span style="color:#212121">:</span><span style="color:#1976D2"> str</span><span style="color:#D32F2F"> =</span><span style="color:#22863A"> ""</span><span style="color:#24292EFF">) </span><span style="color:#212121">-&gt;</span><span style="color:#1976D2"> bool</span><span style="color:#24292EFF">:</span></span>
<span class="line"><span style="color:#C2C3C5">  # 1. 启用流式下载 ：不要一次性将整个文件加载到内存</span></span>
<span class="line"><span style="color:#C2C3C5">  # 后续通过response.iter_content()的方式获取数据</span></span>
<span class="line"><span style="color:#24292EFF">  response </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> requests</span><span style="color:#212121">.</span><span style="color:#6F42C1">get</span><span style="color:#212121">(url, stream</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">  response</span><span style="color:#212121">.</span><span style="color:#6F42C1">raise_for_status</span><span style="color:#212121">()</span><span style="color:#C2C3C5">  # 如果404或500，直接报错</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph">二是有个进度条，方便用户感知下载进度，初始获取所有长度，每次write后更新进度。</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 获取文件大小，为进度条做准备</span></span>
<span class="line"><span style="color:#24292EFF">total_size </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> int</span><span style="color:#212121">(response.headers.</span><span style="color:#6F42C1">get</span><span style="color:#212121">(</span><span style="color:#22863A">'content-length'</span><span style="color:#212121">, </span><span style="color:#1976D2">0</span><span style="color:#212121">))</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 显示进度条</span></span>
<span class="line"><span style="color:#D32F2F">with</span><span style="color:#6F42C1"> tqdm</span><span style="color:#212121">(total</span><span style="color:#D32F2F">=</span><span style="color:#212121">total_size, unit</span><span style="color:#D32F2F">=</span><span style="color:#22863A">'B'</span><span style="color:#212121">, unit_scale</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">, desc</span><span style="color:#D32F2F">=</span><span style="color:#212121">description)</span><span style="color:#D32F2F"> as</span><span style="color:#24292EFF"> pbar</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">    for</span><span style="color:#24292EFF"> chunk </span><span style="color:#D32F2F">in</span><span style="color:#24292EFF"> response</span><span style="color:#212121">.</span><span style="color:#6F42C1">iter_content</span><span style="color:#212121">(chunk_size</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">8192</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#24292EFF"> chunk</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            f</span><span style="color:#212121">.</span><span style="color:#6F42C1">write</span><span style="color:#212121">(chunk)</span></span>
<span class="line"><span style="color:#24292EFF">            pbar</span><span style="color:#212121">.</span><span style="color:#6F42C1">update</span><span style="color:#212121">(</span><span style="color:#6F42C1">len</span><span style="color:#212121">(chunk))</span><span style="color:#C2C3C5">  # 更新进度条</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><ol start="4"><li><p data-v-550653ec="" class="paragraph">合并流程：使用run方法对流程做串联。</p></li></ol><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.1.3 运行效果</strong></h4><p data-v-550653ec="" class="paragraph"><strong>执行:</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#6F42C1">python</span><span style="color:#2B5581"> dataScripts/download_data.py</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 1075px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/4ea6c200-224a-42bb-b2ad-08f55f2fe023.png" alt="图片"></div><!----></div><div data-v-c2512d4e="" class="foldBlock-wrapper" style="background-color: rgba(242, 243, 245, 0.6); border-color: rgba(255, 255, 255, 0);"><div data-v-c2512d4e="" class="collapse-icon"><svg data-v-c2512d4e="" class="icon" viewBox="0 0 1365 1024" xmlns="http://www.w3.org/2000/svg" width="11" height="11"><path d="M121.173333 785.066667l485.717334-580.266667a101.546667 101.546667 0 0 1 151.722666 0L1244.16 785.066667a39.424 39.424 0 0 1 4.778667 44.544 48.298667 48.298667 0 0 1-42.666667 23.722666H159.232a48.298667 48.298667 0 0 1-42.496-23.722666A39.424 39.424 0 0 1 121.173333 785.066667z" fill="#515151" transform="rotate(-90, 682.5, 512)"></path></svg></div><div data-v-c2512d4e="" class="foldBlock-icon">💡</div><div data-v-c2512d4e="" class="foldBlock-inner is-folded"><div data-v-c2512d4e="" class="foldBlock-header"><p data-v-550653ec="" data-v-c2512d4e="" class="paragraph">如果没有数据，需要自己处理数据</p></div><div data-v-c2512d4e="" class="foldBlock-content"><p data-v-550653ec="" data-v-c2512d4e="" class="paragraph">如果没有数据，则需要处理成三元组的形式。</p><p data-v-550653ec="" data-v-c2512d4e="" class="paragraph">参考文本对话抽取：</p><p data-v-550653ec="" data-v-c2512d4e="" class="paragraph"><a target="https://github.com/KMnO4-zx/extract-dialogue" href="https://github.com/KMnO4-zx/extract-dialogue" class="" rel="noopener noreferrer nofollow">https://github.com/KMnO4-zx/extract-dialogue</a></p><p data-v-550653ec="" data-v-c2512d4e="" class="paragraph">​</p><p data-v-550653ec="" data-v-c2512d4e="" class="paragraph">看似是数据下载，其实数据下载包含这几步：</p><p data-v-550653ec="" data-v-c2512d4e="" class="paragraph">​</p><p data-v-550653ec="" data-v-c2512d4e="" class="paragraph">这块不光是数据下载，还包括对话的抽取，最终要体现为我们想要表达角色的形式。</p></div></div></div><p data-v-550653ec="" class="paragraph"></p><h3 class="heading"><strong>4.2 数据预处理模块</strong></h3><div data-v-4d50615b="" class="attachment-block-container"><div data-v-4d50615b="" class="attachment-block-card-container"><div data-v-4d50615b="" class="attachment-block-card-icon"><svg data-v-4d50615b="" width="48" height="48" viewBox="0 0 32 32" class="attachment-block-card-icon-svg"><defs><path d="M1.5 0h14.086a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V26.5a1.5 1.5 0 01-1.5 1.5h-19A1.5 1.5 0 010 26.5v-25A1.5 1.5 0 011.5 0z" id="icon_file_js_svg__a"></path><path d="M16.293.293l5.414 5.414A1 1 0 0121.91 6H17.5A1.5 1.5 0 0116 4.5V.09a1 1 0 01.293.203z" id="icon_file_js_svg__b"></path></defs><g fill="none" fill-rule="evenodd"><g transform="translate(5 2)"><use fill="#eef" xlink:href="#icon_file_js_svg__a"></use><use fill="#ccc" xlink:href="#icon_file_js_svg__b"></use></g><g transform="translate(7 7) scale(0.019)"><path d="M420.693333 85.333333C353.28 85.333333 298.666667 139.946667 298.666667 207.36v71.68h183.04c16.64 0 30.293333 24.32 30.293333 40.96H207.36C139.946667 320 85.333333 374.613333 85.333333 442.026667v161.322666c0 67.413333 54.613333 122.026667 122.026667 122.026667h50.346667v-114.346667c0-67.413333 54.186667-122.026667 121.6-122.026666h224c67.413333 0 122.026667-54.229333 122.026666-121.642667V207.36C725.333333 139.946667 670.72 85.333333 603.306667 85.333333z m-30.72 68.693334c17.066667 0 30.72 5.12 30.72 30.293333s-13.653333 38.016-30.72 38.016c-16.64 0-30.293333-12.8-30.293333-37.973333s13.653333-30.336 30.293333-30.336z" fill="#3C78AA"></path><path d="M766.250667 298.666667v114.346666a121.6 121.6 0 0 1-121.6 121.984H420.693333A121.6 121.6 0 0 0 298.666667 656.597333v160a122.026667 122.026667 0 0 0 122.026666 122.026667h182.613334A122.026667 122.026667 0 0 0 725.333333 816.64v-71.68h-183.082666c-16.64 0-30.250667-24.32-30.250667-40.96h304.64A122.026667 122.026667 0 0 0 938.666667 581.973333v-161.28a122.026667 122.026667 0 0 0-122.026667-122.026666zM354.986667 491.221333l-0.170667 0.170667c0.512-0.085333 1.066667-0.042667 1.621333-0.170667z m279.04 310.442667c16.64 0 30.293333 12.8 30.293333 37.973333a30.293333 30.293333 0 0 1-30.293333 30.293334c-17.066667 0-30.72-5.12-30.72-30.293334s13.653333-37.973333 30.72-37.973333z" fill="#FDD835"></path></g></g></svg></div><div data-v-4d50615b="" class="attachment-block-card-info"><div data-v-4d50615b="" class="attachment-block-card-info-name">huanhuan_data_prepare.py</div><div data-v-4d50615b="" class="attachment-block-card-info-size">6.95KB</div></div><div data-v-4d50615b="" class="attachment-block-card-preview"><div data-v-4d50615b="" class="attachment-block-card-preview-wrapper"><svg data-v-4d50615b="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="VisibleOutlined" class="attachment-block-card-preview-btn"><path d="M11.985 18.5c3.238 0 6.236-2.06 9.015-6.513C18.292 7.55 15.3 5.5 11.985 5.5 8.67 5.5 5.689 7.549 3 11.987c2.76 4.454 5.748 6.513 8.985 6.513ZM1.502 12.89a1.782 1.782 0 0 1 .023-1.838C4.428 6.017 7.915 3.5 11.984 3.5c4.086 0 7.594 2.538 10.523 7.614l.028.048c.296.519.294 1.16-.01 1.675-3.006 5.108-6.52 7.663-10.541 7.663-4.007 0-7.501-2.537-10.482-7.61ZM12 16a4 4 0 1 1 0-8 4 4 0 0 1 0 8Zm0-2a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" fill="rgb(100, 106, 115)"></path></svg></div></div><div data-v-4d50615b="" class="attachment-block-card-menu" style="display: none;"><div data-v-583c080e="" data-v-4d50615b="" class="attachment-menu"><ul data-v-583c080e="" class="attachment-menu-group"><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="MagnifyLeftOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M9 2a1 1 0 0 1 0 2H5.414l5.293 5.293a1 1 0 0 1-1.414 1.414L4 5.414V9a1 1 0 0 1-2 0V3a1 1 0 0 1 1-1h6Zm6 20a1 1 0 1 1 0-2h3.586l-5.293-5.293a1 1 0 0 1 1.414-1.414L20 18.586V15a1 1 0 1 1 2 0v6a1 1 0 0 1-1 1h-6Z" fill="rgb(43, 47, 54)"></path></svg></li><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="DownloadOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M20 18a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-2a1 1 0 1 1 2 0v1h14v-1a1 1 0 0 1 1-1Zm-7-3.964 2.657-2.657a1 1 0 0 1 1.414 1.414c-1.414 1.415-2.828 2.83-4.244 4.244a1 1 0 0 1-1.412 0c-1.417-1.415-2.833-2.833-4.249-4.25a.993.993 0 0 1 .013-1.401.992.992 0 0 1 1.401-.013l2.42 2.42V3.5a1 1 0 1 1 2 0v10.536Z" fill="rgb(43, 47, 54)"></path></svg></li></ul></div></div></div></div><blockquote><p data-v-550653ec="" class="paragraph"><strong>文件位置</strong> : <code>dataScripts/huanhuan_data_prepare.py</code></p></blockquote><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.2.1 解决问题</strong></h4><p data-v-550653ec="" class="paragraph">原始数据是一个JSON文件，包含着甄嬛的对话数据，但是训练模型不能直接吃这种大块数据，需要把它切成小块，分成训练集、验证集和测试集，还要转换成JSONL格式，每行是一个JSON对象。</p><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.2.2 实现流程</strong></h4><p data-v-550653ec="" class="paragraph">预处理需要完成数据的分割。</p><ol start="1"><li><p data-v-550653ec="" class="paragraph">首先，因为预处理模块有配置信息（状态）和执行动作（行为），所以我们也可以通过 <strong>类</strong> 来定义。配置是：输入和输出路径、处理的数据量。</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">class</span><span style="color:#6F42C1"> HuanHuanDataProcessor</span><span style="color:#24292EFF">:</span></span>
<span class="line"><span style="color:#D32F2F">    def</span><span style="color:#6F42C1"> __init__</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#212121">,</span><span style="color:#FF9800"> max_samples</span><span style="color:#212121">:</span><span style="color:#24292EFF"> Optional</span><span style="color:#212121">[</span><span style="color:#1976D2">int</span><span style="color:#212121">]</span><span style="color:#D32F2F"> =</span><span style="color:#1976D2"> None</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#D32F2F">        pass</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><ol start="2"><li><p data-v-550653ec="" class="paragraph">考虑到路径可能不存在，初始化时做配置设置，都保存在/data。</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> __init__</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#212121">,</span><span style="color:#FF9800"> max_samples</span><span style="color:#212121">:</span><span style="color:#24292EFF"> Optional</span><span style="color:#212121">[</span><span style="color:#1976D2">int</span><span style="color:#212121">]</span><span style="color:#D32F2F"> =</span><span style="color:#1976D2"> None</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#C2C3C5">    # 获取当前文件的根目录（dataScripts的上级目录）</span></span>
<span class="line"><span style="color:#24292EFF">    script_dir </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> Path</span><span style="color:#212121">(</span><span style="color:#1976D2">__file__</span><span style="color:#212121">).</span><span style="color:#24292EFF">parent</span></span>
<span class="line"><span style="color:#24292EFF">    project_root </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> script_dir</span><span style="color:#212121">.</span><span style="color:#24292EFF">parent</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 设置输入和输出目录的相对路径</span></span>
<span class="line"><span style="color:#24292EFF">    self</span><span style="color:#212121">.</span><span style="color:#24292EFF">input_dir </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> project_root </span><span style="color:#D32F2F">/</span><span style="color:#22863A"> "data"</span><span style="color:#D32F2F"> /</span><span style="color:#22863A"> "raw"</span></span>
<span class="line"><span style="color:#24292EFF">    self</span><span style="color:#212121">.</span><span style="color:#24292EFF">output_dir </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> project_root </span><span style="color:#D32F2F">/</span><span style="color:#22863A"> "data"</span></span>
<span class="line"><span style="color:#24292EFF">    self</span><span style="color:#212121">.</span><span style="color:#24292EFF">max_samples </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> max_samples</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 检查输入目录是否存在</span></span>
<span class="line"><span style="color:#D32F2F">    if</span><span style="color:#D32F2F"> not</span><span style="color:#24292EFF"> self</span><span style="color:#212121">.</span><span style="color:#24292EFF">input_dir</span><span style="color:#212121">.</span><span style="color:#6F42C1">exists</span><span style="color:#212121">():</span></span>
<span class="line"><span style="color:#24292EFF">        logger</span><span style="color:#212121">.</span><span style="color:#6F42C1">error</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"输入目录不存在: </span><span style="color:#1976D2">{</span><span style="color:#212121">self.input_dir</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">        raise</span><span style="color:#1976D2"> FileNotFoundError</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"输入目录不存在: </span><span style="color:#1976D2">{</span><span style="color:#212121">self.input_dir</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 创建输出目录</span></span>
<span class="line"><span style="color:#24292EFF">    self</span><span style="color:#212121">.</span><span style="color:#24292EFF">output_dir</span><span style="color:#212121">.</span><span style="color:#6F42C1">mkdir</span><span style="color:#212121">(parents</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">, exist_ok</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">)</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><ol start="3"><li><p data-v-550653ec="" class="paragraph">加载数据，读取指定文件的数据，如果数据格式不对则返回错误，加载数据时不限制最大条数，全部加载。</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> load_json_data</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#24292EFF">    all_data </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> []</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 查找huanhuan.json文件</span></span>
<span class="line"><span style="color:#24292EFF">    json_file </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> self</span><span style="color:#212121">.</span><span style="color:#24292EFF">input_dir </span><span style="color:#D32F2F">/</span><span style="color:#22863A"> "huanhuan.json"</span></span>
<span class="line"><span style="color:#D32F2F">    if</span><span style="color:#24292EFF"> json_file</span><span style="color:#212121">.</span><span style="color:#6F42C1">exists</span><span style="color:#212121">():</span></span>
<span class="line"><span style="color:#D32F2F">        try</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">            with</span><span style="color:#6F42C1"> open</span><span style="color:#212121">(json_file, </span><span style="color:#22863A">'r'</span><span style="color:#212121">, encoding</span><span style="color:#D32F2F">=</span><span style="color:#22863A">'utf-8'</span><span style="color:#212121">)</span><span style="color:#D32F2F"> as</span><span style="color:#24292EFF"> f</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                data </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> json</span><span style="color:#212121">.</span><span style="color:#6F42C1">load</span><span style="color:#212121">(f)</span></span>
<span class="line"><span style="color:#D32F2F">                if</span><span style="color:#6F42C1"> isinstance</span><span style="color:#212121">(data, </span><span style="color:#1976D2">list</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#24292EFF">                    all_data</span><span style="color:#212121">.</span><span style="color:#6F42C1">extend</span><span style="color:#212121">(data)</span></span>
<span class="line"><span style="color:#24292EFF">                    logger</span><span style="color:#212121">.</span><span style="color:#6F42C1">info</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"加载训练数据: </span><span style="color:#1976D2">{</span><span style="color:#6F42C1">len</span><span style="color:#212121">(data)</span><span style="color:#1976D2">}</span><span style="color:#22863A"> 条"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">                else</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                    logger</span><span style="color:#212121">.</span><span style="color:#6F42C1">warning</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"数据格式不正确: </span><span style="color:#1976D2">{</span><span style="color:#212121">json_file.name</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">        except</span><span style="color:#1976D2"> Exception</span><span style="color:#D32F2F"> as</span><span style="color:#24292EFF"> e</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            logger</span><span style="color:#212121">.</span><span style="color:#6F42C1">error</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"加载数据失败: </span><span style="color:#1976D2">{</span><span style="color:#212121">e</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">    else</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">        logger</span><span style="color:#212121">.</span><span style="color:#6F42C1">error</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"未找到训练数据文件: </span><span style="color:#1976D2">{</span><span style="color:#212121">json_file</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    return</span><span style="color:#24292EFF"> all_data</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><ol start="4"><li><p data-v-550653ec="" class="paragraph">处理数据，从加载的全部数据中，随机采样指定的数据量。</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> process_data</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#24292EFF">    ...</span></span>
<span class="line"><span style="color:#C2C3C5">    # 如果指定了最大样本数，则限制数据量</span></span>
<span class="line"><span style="color:#D32F2F">    if</span><span style="color:#24292EFF"> self</span><span style="color:#212121">.</span><span style="color:#24292EFF">max_samples </span><span style="color:#D32F2F">and</span><span style="color:#24292EFF"> self</span><span style="color:#212121">.</span><span style="color:#24292EFF">max_samples </span><span style="color:#D32F2F">&lt;</span><span style="color:#6F42C1"> len</span><span style="color:#212121">(valid_data):</span></span>
<span class="line"><span style="color:#C2C3C5">        # 随机采样指定数量的数据</span></span>
<span class="line"><span style="color:#24292EFF">        random</span><span style="color:#212121">.</span><span style="color:#6F42C1">shuffle</span><span style="color:#212121">(valid_data)</span></span>
<span class="line"><span style="color:#24292EFF">        valid_data </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> valid_data</span><span style="color:#212121">[:</span><span style="color:#24292EFF">self</span><span style="color:#212121">.</span><span style="color:#24292EFF">max_samples</span><span style="color:#212121">]</span></span>
<span class="line"><span style="color:#24292EFF">        logger</span><span style="color:#212121">.</span><span style="color:#6F42C1">info</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"限制数据量为: </span><span style="color:#1976D2">{</span><span style="color:#212121">self.max_samples</span><span style="color:#1976D2">}</span><span style="color:#22863A"> 条"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">    return</span><span style="color:#24292EFF"> valid_data</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><ol start="5"><li><p data-v-550653ec="" class="paragraph">数据分割，按照机器学习的标准做法，将数据分为训练集（80%）、验证集（10%）和测试集（10%）。</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> split_data</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#212121">,</span><span style="color:#FF9800"> data</span><span style="color:#212121">:</span><span style="color:#24292EFF"> List</span><span style="color:#212121">[</span><span style="color:#24292EFF">Dict</span><span style="color:#212121">]</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#24292EFF">    random</span><span style="color:#212121">.</span><span style="color:#6F42C1">shuffle</span><span style="color:#212121">(data)</span><span style="color:#C2C3C5">  # 随机打乱数据</span></span>
<span class="line"><span style="color:#24292EFF"> </span></span>
<span class="line"><span style="color:#24292EFF">    total_size </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> len</span><span style="color:#212121">(data)</span></span>
<span class="line"><span style="color:#24292EFF">    train_size </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> int</span><span style="color:#212121">(total_size </span><span style="color:#D32F2F">*</span><span style="color:#1976D2"> 0.8</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    val_size </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> int</span><span style="color:#212121">(total_size </span><span style="color:#D32F2F">*</span><span style="color:#1976D2"> 0.1</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#24292EFF">    train_data </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> data</span><span style="color:#212121">[:</span><span style="color:#24292EFF">train_size</span><span style="color:#212121">]</span></span>
<span class="line"><span style="color:#24292EFF">    val_data </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> data</span><span style="color:#212121">[</span><span style="color:#24292EFF">train_size</span><span style="color:#212121">:</span><span style="color:#24292EFF">train_size </span><span style="color:#D32F2F">+</span><span style="color:#24292EFF"> val_size</span><span style="color:#212121">]</span></span>
<span class="line"><span style="color:#24292EFF">    test_data </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> data</span><span style="color:#212121">[</span><span style="color:#24292EFF">train_size </span><span style="color:#D32F2F">+</span><span style="color:#24292EFF"> val_size</span><span style="color:#212121">:]</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    return</span><span style="color:#24292EFF"> train_data</span><span style="color:#212121">,</span><span style="color:#24292EFF"> val_data</span><span style="color:#212121">,</span><span style="color:#24292EFF"> test_data</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><ol start="6"><li><p data-v-550653ec="" class="paragraph">保存数据，整合流程；</p></li></ol><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.2.3 运行效果</strong></h4><p data-v-550653ec="" class="paragraph"><strong>执行</strong> :</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 处理全部数据</span></span>
<span class="line"><span style="color:#6F42C1">python</span><span style="color:#2B5581"> dataScripts/huanhuan_data_prepare.py</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 只处理50条数据（用于快速测试）</span></span>
<span class="line"><span style="color:#6F42C1">python</span><span style="color:#2B5581"> dataScripts/huanhuan_data_prepare.py</span><span style="color:#1976D2"> 50</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 1021px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/7891db64-c031-4c44-9208-6bcc03e2b18d.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph">输出的数据内容，每一行为一个JSON：</p><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 754px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/4e50697c-50c0-4eac-88ca-b7b25ee03f5f.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph"></p><h3 class="heading"><strong>4.3 模型训练模块</strong></h3><div data-v-4d50615b="" class="attachment-block-container"><div data-v-4d50615b="" class="attachment-block-card-container"><div data-v-4d50615b="" class="attachment-block-card-icon"><svg data-v-4d50615b="" width="48" height="48" viewBox="0 0 32 32" class="attachment-block-card-icon-svg"><defs><path d="M1.5 0h14.086a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V26.5a1.5 1.5 0 01-1.5 1.5h-19A1.5 1.5 0 010 26.5v-25A1.5 1.5 0 011.5 0z" id="icon_file_js_svg__a"></path><path d="M16.293.293l5.414 5.414A1 1 0 0121.91 6H17.5A1.5 1.5 0 0116 4.5V.09a1 1 0 01.293.203z" id="icon_file_js_svg__b"></path></defs><g fill="none" fill-rule="evenodd"><g transform="translate(5 2)"><use fill="#eef" xlink:href="#icon_file_js_svg__a"></use><use fill="#ccc" xlink:href="#icon_file_js_svg__b"></use></g><g transform="translate(7 7) scale(0.019)"><path d="M420.693333 85.333333C353.28 85.333333 298.666667 139.946667 298.666667 207.36v71.68h183.04c16.64 0 30.293333 24.32 30.293333 40.96H207.36C139.946667 320 85.333333 374.613333 85.333333 442.026667v161.322666c0 67.413333 54.613333 122.026667 122.026667 122.026667h50.346667v-114.346667c0-67.413333 54.186667-122.026667 121.6-122.026666h224c67.413333 0 122.026667-54.229333 122.026666-121.642667V207.36C725.333333 139.946667 670.72 85.333333 603.306667 85.333333z m-30.72 68.693334c17.066667 0 30.72 5.12 30.72 30.293333s-13.653333 38.016-30.72 38.016c-16.64 0-30.293333-12.8-30.293333-37.973333s13.653333-30.336 30.293333-30.336z" fill="#3C78AA"></path><path d="M766.250667 298.666667v114.346666a121.6 121.6 0 0 1-121.6 121.984H420.693333A121.6 121.6 0 0 0 298.666667 656.597333v160a122.026667 122.026667 0 0 0 122.026666 122.026667h182.613334A122.026667 122.026667 0 0 0 725.333333 816.64v-71.68h-183.082666c-16.64 0-30.250667-24.32-30.250667-40.96h304.64A122.026667 122.026667 0 0 0 938.666667 581.973333v-161.28a122.026667 122.026667 0 0 0-122.026667-122.026666zM354.986667 491.221333l-0.170667 0.170667c0.512-0.085333 1.066667-0.042667 1.621333-0.170667z m279.04 310.442667c16.64 0 30.293333 12.8 30.293333 37.973333a30.293333 30.293333 0 0 1-30.293333 30.293334c-17.066667 0-30.72-5.12-30.72-30.293334s13.653333-37.973333 30.72-37.973333z" fill="#FDD835"></path></g></g></svg></div><div data-v-4d50615b="" class="attachment-block-card-info"><div data-v-4d50615b="" class="attachment-block-card-info-name">huanhuan_train.py</div><div data-v-4d50615b="" class="attachment-block-card-info-size">14.72KB</div></div><div data-v-4d50615b="" class="attachment-block-card-preview"><div data-v-4d50615b="" class="attachment-block-card-preview-wrapper"><svg data-v-4d50615b="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="VisibleOutlined" class="attachment-block-card-preview-btn"><path d="M11.985 18.5c3.238 0 6.236-2.06 9.015-6.513C18.292 7.55 15.3 5.5 11.985 5.5 8.67 5.5 5.689 7.549 3 11.987c2.76 4.454 5.748 6.513 8.985 6.513ZM1.502 12.89a1.782 1.782 0 0 1 .023-1.838C4.428 6.017 7.915 3.5 11.984 3.5c4.086 0 7.594 2.538 10.523 7.614l.028.048c.296.519.294 1.16-.01 1.675-3.006 5.108-6.52 7.663-10.541 7.663-4.007 0-7.501-2.537-10.482-7.61ZM12 16a4 4 0 1 1 0-8 4 4 0 0 1 0 8Zm0-2a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" fill="rgb(100, 106, 115)"></path></svg></div></div><div data-v-4d50615b="" class="attachment-block-card-menu" style="display: none;"><div data-v-583c080e="" data-v-4d50615b="" class="attachment-menu"><ul data-v-583c080e="" class="attachment-menu-group"><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="MagnifyLeftOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M9 2a1 1 0 0 1 0 2H5.414l5.293 5.293a1 1 0 0 1-1.414 1.414L4 5.414V9a1 1 0 0 1-2 0V3a1 1 0 0 1 1-1h6Zm6 20a1 1 0 1 1 0-2h3.586l-5.293-5.293a1 1 0 0 1 1.414-1.414L20 18.586V15a1 1 0 1 1 2 0v6a1 1 0 0 1-1 1h-6Z" fill="rgb(43, 47, 54)"></path></svg></li><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="DownloadOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M20 18a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-2a1 1 0 1 1 2 0v1h14v-1a1 1 0 0 1 1-1Zm-7-3.964 2.657-2.657a1 1 0 0 1 1.414 1.414c-1.414 1.415-2.828 2.83-4.244 4.244a1 1 0 0 1-1.412 0c-1.417-1.415-2.833-2.833-4.249-4.25a.993.993 0 0 1 .013-1.401.992.992 0 0 1 1.401-.013l2.42 2.42V3.5a1 1 0 1 1 2 0v10.536Z" fill="rgb(43, 47, 54)"></path></svg></li></ul></div></div></div></div><div data-v-4d50615b="" class="attachment-block-container"><div data-v-4d50615b="" class="attachment-block-card-container"><div data-v-4d50615b="" class="attachment-block-card-icon"><svg data-v-4d50615b="" width="48" height="48" viewBox="0 0 32 32" class="attachment-block-card-icon-svg"><defs><path d="M1.5 0h14.086a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V26.5a1.5 1.5 0 01-1.5 1.5h-19A1.5 1.5 0 010 26.5v-25A1.5 1.5 0 011.5 0z" id="icon_file_unknow_nor_svg__a"></path><path d="M16.293.293l5.414 5.414A1 1 0 0121.91 6H17.5A1.5 1.5 0 0116 4.5V.09a1 1 0 01.293.203z" id="icon_file_unknow_nor_svg__b"></path></defs><g fill="none" fill-rule="evenodd"><g transform="translate(5 2)"><use fill="#9CA3AD" xlink:href="#icon_file_unknow_nor_svg__a"></use><use fill="#646A73" xlink:href="#icon_file_unknow_nor_svg__b"></use></g><path d="M7.649 10.703h16.648V27.35H7.65z"></path><path d="M16.16 12.216c1.236 0 2.23.356 2.987 1.073.72.681 1.081 1.602 1.081 2.749a3.66 3.66 0 01-.76 2.255c-.209.266-.676.72-1.518 1.482-.42.367-.661.618-.849.937-.25.43-.371.87-.371 1.361v.549a.19.19 0 01-.19.189h-1.154a.19.19 0 01-.189-.19v-.548c0-.58.121-1.12.359-1.594.288-.618.835-1.247 1.72-2.02.56-.56.733-.74.854-.891.38-.488.566-.978.566-1.492 0-.754-.221-1.355-.645-1.796-.444-.445-1.088-.664-1.947-.664-.963 0-1.664.314-2.142.95-.378.474-.59.9-.63 1.713a9.425 9.425 0 00-.006.29.19.19 0 01-.19.188h-1.135a.19.19 0 01-.189-.191c.001-.15.003-.248.005-.296.052-1.177.411-1.923 1.08-2.702.787-.907 1.875-1.352 3.263-1.352zm-.755 12.108h1.136a.19.19 0 01.189.19v1.135a.19.19 0 01-.19.189h-1.135a.19.19 0 01-.189-.19v-1.134a.19.19 0 01.19-.19z" fill="#FFF" fill-rule="nonzero"></path></g></svg></div><div data-v-4d50615b="" class="attachment-block-card-info"><div data-v-4d50615b="" class="attachment-block-card-info-name">huanhuan_config_fast.yaml</div><div data-v-4d50615b="" class="attachment-block-card-info-size">6.34KB</div></div><div data-v-4d50615b="" class="attachment-block-card-preview"><div data-v-4d50615b="" class="attachment-block-card-preview-wrapper"><svg data-v-4d50615b="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="VisibleOutlined" class="attachment-block-card-preview-btn"><path d="M11.985 18.5c3.238 0 6.236-2.06 9.015-6.513C18.292 7.55 15.3 5.5 11.985 5.5 8.67 5.5 5.689 7.549 3 11.987c2.76 4.454 5.748 6.513 8.985 6.513ZM1.502 12.89a1.782 1.782 0 0 1 .023-1.838C4.428 6.017 7.915 3.5 11.984 3.5c4.086 0 7.594 2.538 10.523 7.614l.028.048c.296.519.294 1.16-.01 1.675-3.006 5.108-6.52 7.663-10.541 7.663-4.007 0-7.501-2.537-10.482-7.61ZM12 16a4 4 0 1 1 0-8 4 4 0 0 1 0 8Zm0-2a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" fill="rgb(100, 106, 115)"></path></svg></div></div><div data-v-4d50615b="" class="attachment-block-card-menu" style="display: none;"><div data-v-583c080e="" data-v-4d50615b="" class="attachment-menu"><ul data-v-583c080e="" class="attachment-menu-group"><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="MagnifyLeftOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M9 2a1 1 0 0 1 0 2H5.414l5.293 5.293a1 1 0 0 1-1.414 1.414L4 5.414V9a1 1 0 0 1-2 0V3a1 1 0 0 1 1-1h6Zm6 20a1 1 0 1 1 0-2h3.586l-5.293-5.293a1 1 0 0 1 1.414-1.414L20 18.586V15a1 1 0 1 1 2 0v6a1 1 0 0 1-1 1h-6Z" fill="rgb(43, 47, 54)"></path></svg></li><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="DownloadOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M20 18a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-2a1 1 0 1 1 2 0v1h14v-1a1 1 0 0 1 1-1Zm-7-3.964 2.657-2.657a1 1 0 0 1 1.414 1.414c-1.414 1.415-2.828 2.83-4.244 4.244a1 1 0 0 1-1.412 0c-1.417-1.415-2.833-2.833-4.249-4.25a.993.993 0 0 1 .013-1.401.992.992 0 0 1 1.401-.013l2.42 2.42V3.5a1 1 0 1 1 2 0v10.536Z" fill="rgb(43, 47, 54)"></path></svg></li></ul></div></div></div></div><p data-v-550653ec="" class="paragraph">（如果电脑本身内存小，选用fast参数）</p><blockquote><p data-v-550653ec="" class="paragraph"><strong>文件位置</strong> : <code>training/huanhuan_train.py</code></p></blockquote><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.3.1 解决问题</strong></h4><p data-v-550653ec="" class="paragraph">模型训练模块解决的问题是将预处理后的甄嬛传数据转换为具有甄嬛语言风格的对话模型 。该模块采用LoRA（Low-Rank Adaptation）微调技术，在保持基础模型能力的同时，让模型学会甄嬛的说话方式和语言特色。</p><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.3.2 理解四个组件的关系</strong></h4><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 2025px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/1336696f-1dbb-45ce-b635-50742ead9d8c.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph">要实现模型训练模块，我们首先需要知道我们训练的是什么，本质上我们是训练基础模型去学会特定的说话风格。这个过程就涉及到了四个组件：基础模型、分词器、LoRA适配器、训练参数。</p><ul><li><p data-v-550653ec="" class="paragraph">这里的基础模型，我们使用的是Qwen2.5-0.5B，该模型提供了基础的语言理解和生成能力，同时Ollama也原生支持，后续部署时可以直接通过ollama来拉取和加载；</p></li><li><p data-v-550653ec="" class="paragraph">分词器，可以理解为语言翻译，它会将文本转成向量，又能将模型输出的向量转回为可读文本；</p></li><li><p data-v-550653ec="" class="paragraph">LoRA适配器，做个性化训练，保持原来的模型参数不变，通过在注意力层添加小型矩阵，让模型学会甄嬛的风格，做到角色塑造；</p></li><li><p data-v-550653ec="" class="paragraph">训练参数，提供的是学习策略，控制了学习的快慢、每次看多少内容、内容学几遍等；</p></li></ul><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.3.3 实现流程</strong></h4><p data-v-550653ec="" class="paragraph">训练模块的实现相对复杂，需要解决配置管理、设备适配、数据加载、模型微调等多个问题。让我们逐步分析：</p><p data-v-550653ec="" class="paragraph"></p><ol start="1"><li><p data-v-550653ec="" class="paragraph">首先，同样训练器必然有配置信息和训练动作（状态和行为），通过类来定义。</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">class</span><span style="color:#6F42C1"> HuanHuanTrainer</span><span style="color:#24292EFF">:</span></span>
<span class="line"><span style="color:#D32F2F">    def</span><span style="color:#6F42C1"> __init__</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#212121">,</span><span style="color:#FF9800"> config_path</span><span style="color:#212121">:</span><span style="color:#1976D2"> str</span><span style="color:#D32F2F"> =</span><span style="color:#22863A"> "./huanhuan_config_fast.yaml"</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#D32F2F">        pass</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><ol start="2"><li><p data-v-550653ec="" class="paragraph">训练需要多个组件协同，在初始化时对组件进行配置，配置文件统一由huanhuan_config.yaml进行管理；</p></li><li><p data-v-550653ec="" class="paragraph">训练设备检测，适配机器选择相应设备做训练，本机是在mac运行，因此用的是MPS做的训练，win设备至少可以兼容CPU。</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> setup_device</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#24292EFF">    device_config </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> self</span><span style="color:#212121">.</span><span style="color:#24292EFF">config</span><span style="color:#212121">[</span><span style="color:#22863A">'system'</span><span style="color:#212121">]</span><span style="color:#24292EFF">[</span><span style="color:#22863A">'device'</span><span style="color:#24292EFF">]</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    if</span><span style="color:#24292EFF"> device_config </span><span style="color:#D32F2F">==</span><span style="color:#22863A"> 'auto'</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#24292EFF"> torch</span><span style="color:#212121">.</span><span style="color:#24292EFF">cuda</span><span style="color:#212121">.</span><span style="color:#6F42C1">is_available</span><span style="color:#212121">():</span></span>
<span class="line"><span style="color:#24292EFF">            self</span><span style="color:#212121">.</span><span style="color:#24292EFF">device </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> torch</span><span style="color:#212121">.</span><span style="color:#6F42C1">device</span><span style="color:#212121">(</span><span style="color:#22863A">'cuda'</span><span style="color:#212121">)</span><span style="color:#C2C3C5">  # 优先使用CUDA</span></span>
<span class="line"><span style="color:#24292EFF">            logger</span><span style="color:#212121">.</span><span style="color:#6F42C1">info</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"使用CUDA设备: </span><span style="color:#1976D2">{</span><span style="color:#212121">torch.cuda.</span><span style="color:#6F42C1">get_device_name</span><span style="color:#212121">()</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">        elif</span><span style="color:#6F42C1"> hasattr</span><span style="color:#212121">(torch.backends, </span><span style="color:#22863A">'mps'</span><span style="color:#212121">)</span><span style="color:#D32F2F"> and</span><span style="color:#24292EFF"> torch</span><span style="color:#212121">.</span><span style="color:#24292EFF">backends</span><span style="color:#212121">.</span><span style="color:#24292EFF">mps</span><span style="color:#212121">.</span><span style="color:#6F42C1">is_available</span><span style="color:#212121">():</span></span>
<span class="line"><span style="color:#24292EFF">            self</span><span style="color:#212121">.</span><span style="color:#24292EFF">device </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> torch</span><span style="color:#212121">.</span><span style="color:#6F42C1">device</span><span style="color:#212121">(</span><span style="color:#22863A">'mps'</span><span style="color:#212121">)</span><span style="color:#C2C3C5">   # 其次使用Apple Silicon MPS</span></span>
<span class="line"><span style="color:#24292EFF">            logger</span><span style="color:#212121">.</span><span style="color:#6F42C1">info</span><span style="color:#212121">(</span><span style="color:#22863A">"使用Apple Silicon MPS设备"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">        else</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            self</span><span style="color:#212121">.</span><span style="color:#24292EFF">device </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> torch</span><span style="color:#212121">.</span><span style="color:#6F42C1">device</span><span style="color:#212121">(</span><span style="color:#22863A">'cpu'</span><span style="color:#212121">)</span><span style="color:#C2C3C5">   # 最后使用CPU</span></span>
<span class="line"><span style="color:#24292EFF">            logger</span><span style="color:#212121">.</span><span style="color:#6F42C1">info</span><span style="color:#212121">(</span><span style="color:#22863A">"使用CPU设备"</span><span style="color:#212121">)</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><ol start="4"><li><p data-v-550653ec="" class="paragraph">数据集处理，将JSONL格式数据转换为模型可用的Dataset。模型可用指的是模型可以直接使用的训练数据，即转化为前面提到的向量，通过HuanHuanDataset类来实现。、</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 原始JSONL数据（人类可读）</span></span>
<span class="line"><span style="color:#212121">{</span><span style="color:#22863A">"instruction"</span><span style="color:#212121">:</span><span style="color:#22863A"> "请问甄嬛的性格特点"</span><span style="color:#212121">,</span><span style="color:#22863A"> "input"</span><span style="color:#212121">:</span><span style="color:#22863A"> ""</span><span style="color:#212121">,</span><span style="color:#22863A"> "output"</span><span style="color:#212121">:</span><span style="color:#22863A"> "臣妾性格温婉..."</span><span style="color:#212121">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 转换为模型可用格式（机器可读）</span></span>
<span class="line"><span style="color:#212121">{</span></span>
<span class="line"><span style="color:#22863A">    'input_ids'</span><span style="color:#212121">:</span><span style="color:#6F42C1"> tensor</span><span style="color:#212121">([</span><span style="color:#1976D2">123</span><span style="color:#212121">, </span><span style="color:#1976D2">456</span><span style="color:#212121">, </span><span style="color:#1976D2">789</span><span style="color:#212121">, ...]),</span><span style="color:#C2C3C5">      # Token序列</span></span>
<span class="line"><span style="color:#22863A">    'attention_mask'</span><span style="color:#212121">:</span><span style="color:#6F42C1"> tensor</span><span style="color:#212121">([</span><span style="color:#1976D2">1</span><span style="color:#212121">, </span><span style="color:#1976D2">1</span><span style="color:#212121">, </span><span style="color:#1976D2">1</span><span style="color:#212121">, ...]),</span><span style="color:#C2C3C5">       # 注意力掩码</span></span>
<span class="line"><span style="color:#22863A">    'labels'</span><span style="color:#212121">:</span><span style="color:#6F42C1"> tensor</span><span style="color:#212121">([</span><span style="color:#D32F2F">-</span><span style="color:#1976D2">100</span><span style="color:#212121">, </span><span style="color:#D32F2F">-</span><span style="color:#1976D2">100</span><span style="color:#212121">, </span><span style="color:#1976D2">456</span><span style="color:#212121">, ...])</span><span style="color:#C2C3C5">        # 训练标签</span></span>
<span class="line"><span style="color:#212121">}</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><ol start="5"><li><p data-v-550653ec="" class="paragraph">LoRA微调，做参数微调的本质是在指定的模块插入新的矩阵分解层，通过PEFT 库封装了复杂的实现细节，用户只需要配置参数，不需要手写矩阵分解。</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> setup_lora</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#24292EFF">    lora_config </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> LoraConfig</span><span style="color:#212121">(</span></span>
<span class="line"><span style="color:#212121">        task_type</span><span style="color:#D32F2F">=</span><span style="color:#212121">TaskType.CAUSAL_LM,</span></span>
<span class="line"><span style="color:#212121">        r</span><span style="color:#D32F2F">=</span><span style="color:#212121">self.config[</span><span style="color:#22863A">'lora'</span><span style="color:#212121">].</span><span style="color:#6F42C1">get</span><span style="color:#212121">(</span><span style="color:#22863A">'r'</span><span style="color:#212121">, </span><span style="color:#1976D2">8</span><span style="color:#212121">),                    </span><span style="color:#C2C3C5"># rank值，控制参数量</span></span>
<span class="line"><span style="color:#212121">        lora_alpha</span><span style="color:#D32F2F">=</span><span style="color:#212121">self.config[</span><span style="color:#22863A">'lora'</span><span style="color:#212121">].</span><span style="color:#6F42C1">get</span><span style="color:#212121">(</span><span style="color:#22863A">'lora_alpha'</span><span style="color:#212121">, </span><span style="color:#1976D2">16</span><span style="color:#212121">), </span><span style="color:#C2C3C5"># 缩放因子</span></span>
<span class="line"><span style="color:#212121">        lora_dropout</span><span style="color:#D32F2F">=</span><span style="color:#212121">self.config[</span><span style="color:#22863A">'lora'</span><span style="color:#212121">].</span><span style="color:#6F42C1">get</span><span style="color:#212121">(</span><span style="color:#22863A">'lora_dropout'</span><span style="color:#212121">, </span><span style="color:#1976D2">0.1</span><span style="color:#212121">),</span></span>
<span class="line"><span style="color:#212121">        target_modules</span><span style="color:#D32F2F">=</span><span style="color:#212121">self.config[</span><span style="color:#22863A">'lora'</span><span style="color:#212121">].</span><span style="color:#6F42C1">get</span><span style="color:#212121">(</span><span style="color:#22863A">'target_modules'</span><span style="color:#212121">, [</span><span style="color:#22863A">"q_proj"</span><span style="color:#212121">, </span><span style="color:#22863A">"v_proj"</span><span style="color:#212121">]),</span></span>
<span class="line"><span style="color:#212121">        bias</span><span style="color:#D32F2F">=</span><span style="color:#212121">self.config[</span><span style="color:#22863A">'lora'</span><span style="color:#212121">].</span><span style="color:#6F42C1">get</span><span style="color:#212121">(</span><span style="color:#22863A">'bias'</span><span style="color:#212121">, </span><span style="color:#22863A">'none'</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#212121">    )</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#24292EFF">    self</span><span style="color:#212121">.</span><span style="color:#24292EFF">model </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> get_peft_model</span><span style="color:#212121">(self.model, lora_config)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 打印可训练参数统计</span></span>
<span class="line"><span style="color:#24292EFF">    trainable_params </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> sum</span><span style="color:#212121">(p.</span><span style="color:#6F42C1">numel</span><span style="color:#212121">() </span><span style="color:#D32F2F">for</span><span style="color:#212121"> p </span><span style="color:#D32F2F">in</span><span style="color:#212121"> self.model.</span><span style="color:#6F42C1">parameters</span><span style="color:#212121">() </span><span style="color:#D32F2F">if</span><span style="color:#212121"> p.requires_grad)</span></span>
<span class="line"><span style="color:#24292EFF">    total_params </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> sum</span><span style="color:#212121">(p.</span><span style="color:#6F42C1">numel</span><span style="color:#212121">() </span><span style="color:#D32F2F">for</span><span style="color:#212121"> p </span><span style="color:#D32F2F">in</span><span style="color:#212121"> self.model.</span><span style="color:#6F42C1">parameters</span><span style="color:#212121">())</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#24292EFF">    logger</span><span style="color:#212121">.</span><span style="color:#6F42C1">info</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"可训练参数: </span><span style="color:#1976D2">{</span><span style="color:#212121">trainable_params</span><span style="color:#D32F2F">:,</span><span style="color:#1976D2">}</span><span style="color:#22863A"> (</span><span style="color:#1976D2">{100</span><span style="color:#D32F2F"> *</span><span style="color:#212121"> trainable_params </span><span style="color:#D32F2F">/</span><span style="color:#212121"> total_params</span><span style="color:#D32F2F">:.2f</span><span style="color:#1976D2">}</span><span style="color:#22863A">%)"</span><span style="color:#212121">)</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><ol start="6"><li><p data-v-550653ec="" class="paragraph">流程合并，用train方法对整个训练流程做串联；</p></li></ol><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.3.4 运行效果</strong></h4><p data-v-550653ec="" class="paragraph"><strong>执行</strong> :</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 开始训练</span></span>
<span class="line"><span style="color:#6F42C1">python</span><span style="color:#2B5581"> training/huanhuan_train.py</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 在其他窗口同时启动监控（推荐）</span></span>
<span class="line"><span style="color:#6F42C1">python</span><span style="color:#2B5581"> training/sync_monitor.py</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph">训练完成50条数据需要5分钟的时间。</p><ul><li><p data-v-550653ec="" class="paragraph">python training/huanhuan_train.py</p></li></ul><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 1236px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/886309e0-b468-469a-bf58-41629cc284b6.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph">训练的同时开启监控，监控功能用于保存监控报告和实时利用率显示：</p><ul><li><p data-v-550653ec="" class="paragraph"><code>python sync_monitor.py</code></p></li></ul><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 1238px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/52bdac9c-4122-441d-88b2-98c5ee485e28.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.3.5 常见问题处理</strong></h4><p data-v-550653ec="" class="paragraph"><strong>MPS内存不足错误</strong> :</p><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 1413px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/1046f6a4-7fd3-43ef-a39b-dbdbbdf0cacd.png" alt="图片"></div><!----></div><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#1976D2">2025</span><span style="color:#D32F2F">-</span><span style="color:#1976D2">07</span><span style="color:#D32F2F">-</span><span style="color:#1976D2">16</span><span style="color:#1976D2"> 21</span><span style="color:#212121">:</span><span style="color:#1976D2">10</span><span style="color:#212121">:</span><span style="color:#1976D2">20.705</span><span style="color:#D32F2F"> |</span><span style="color:#24292EFF"> ERROR    </span><span style="color:#D32F2F">|</span><span style="color:#24292EFF"> __main__</span><span style="color:#212121">:</span><span style="color:#24292EFF">main</span><span style="color:#212121">:</span><span style="color:#1976D2">427</span><span style="color:#D32F2F"> -</span><span style="color:#24292EFF"> 训练过程出错</span><span style="color:#212121">:</span><span style="color:#24292EFF"> MPS backend out of </span><span style="color:#6F42C1">memory </span><span style="color:#212121">(MPS allocated: </span><span style="color:#1976D2">35.21</span><span style="color:#212121"> GB, other allocations: </span><span style="color:#1976D2">1.02</span><span style="color:#212121"> GB, </span><span style="color:#6F42C1">max</span><span style="color:#212121"> allowed: </span><span style="color:#1976D2">36.27</span><span style="color:#212121"> GB).</span><span style="color:#24292EFF"> Tried to allocate </span><span style="color:#1976D2">172.00</span><span style="color:#24292EFF"> MB on private pool</span><span style="color:#212121">.</span><span style="color:#24292EFF"> Use PYTORCH_MPS_HIGH_WATERMARK_RATIO</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">0.0</span><span style="color:#24292EFF"> to disable upper limit </span><span style="color:#D32F2F">for</span><span style="color:#24292EFF"> memory </span><span style="color:#6F42C1">allocations </span><span style="color:#212121">(may cause system failure).</span></span>
<span class="line"><span style="color:#24292EFF">...</span></span>
<span class="line"><span style="color:#1976D2">RuntimeError</span><span style="color:#212121">:</span><span style="color:#24292EFF"> MPS backend out of </span><span style="color:#6F42C1">memory </span><span style="color:#212121">(MPS allocated: </span><span style="color:#1976D2">35.21</span><span style="color:#212121"> GB, other allocations: </span><span style="color:#1976D2">1.02</span><span style="color:#212121"> GB, </span><span style="color:#6F42C1">max</span><span style="color:#212121"> allowed: </span><span style="color:#1976D2">36.27</span><span style="color:#212121"> GB).</span><span style="color:#24292EFF"> Tried to allocate </span><span style="color:#1976D2">172.00</span><span style="color:#24292EFF"> MB on private pool</span><span style="color:#212121">.</span><span style="color:#24292EFF"> Use PYTORCH_MPS_HIGH_WATERMARK_RATIO=</span><span style="color:#1976D2">0.0</span><span style="color:#24292EFF"> to disable upper limit </span><span style="color:#D32F2F">for</span><span style="color:#24292EFF"> memory </span><span style="color:#6F42C1">allocations </span><span style="color:#212121">(may cause system failure).</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph">由于 Apple Silicon Mac 的 MPS (Metal Performance Shaders) 后端内存不足导致。</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 设置MPS内存限制</span></span>
<span class="line"><span style="color:#D32F2F">export</span><span style="color:#24292EFF"> PYTORCH_MPS_HIGH_WATERMARK_RATIO</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">0.0</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 或在代码中设置更小的batch size</span></span>
<span class="line"><span style="color:#6F42C1">per_device_train_batch_size:</span><span style="color:#1976D2"> 1</span></span>
<span class="line"><span style="color:#6F42C1">gradient_accumulation_steps:</span><span style="color:#1976D2"> 2</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>训练优化策略</strong> :</p><ol start="1"><li><p data-v-550653ec="" class="paragraph"><strong>内存优化</strong> : 启用梯度检查点，减小batch size</p></li><li><p data-v-550653ec="" class="paragraph"><strong>速度优化</strong> : 使用极简LoRA配置，减少可训练参数</p></li><li><p data-v-550653ec="" class="paragraph"><strong>稳定性</strong> : 早停机制，防止过拟合</p></li></ol><p data-v-550653ec="" class="paragraph"></p><h3 class="heading"><strong>4.4 训练监控模块</strong></h3><div data-v-4d50615b="" class="attachment-block-container"><div data-v-4d50615b="" class="attachment-block-card-container"><div data-v-4d50615b="" class="attachment-block-card-icon"><svg data-v-4d50615b="" width="48" height="48" viewBox="0 0 32 32" class="attachment-block-card-icon-svg"><defs><path d="M1.5 0h14.086a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V26.5a1.5 1.5 0 01-1.5 1.5h-19A1.5 1.5 0 010 26.5v-25A1.5 1.5 0 011.5 0z" id="icon_file_js_svg__a"></path><path d="M16.293.293l5.414 5.414A1 1 0 0121.91 6H17.5A1.5 1.5 0 0116 4.5V.09a1 1 0 01.293.203z" id="icon_file_js_svg__b"></path></defs><g fill="none" fill-rule="evenodd"><g transform="translate(5 2)"><use fill="#eef" xlink:href="#icon_file_js_svg__a"></use><use fill="#ccc" xlink:href="#icon_file_js_svg__b"></use></g><g transform="translate(7 7) scale(0.019)"><path d="M420.693333 85.333333C353.28 85.333333 298.666667 139.946667 298.666667 207.36v71.68h183.04c16.64 0 30.293333 24.32 30.293333 40.96H207.36C139.946667 320 85.333333 374.613333 85.333333 442.026667v161.322666c0 67.413333 54.613333 122.026667 122.026667 122.026667h50.346667v-114.346667c0-67.413333 54.186667-122.026667 121.6-122.026666h224c67.413333 0 122.026667-54.229333 122.026666-121.642667V207.36C725.333333 139.946667 670.72 85.333333 603.306667 85.333333z m-30.72 68.693334c17.066667 0 30.72 5.12 30.72 30.293333s-13.653333 38.016-30.72 38.016c-16.64 0-30.293333-12.8-30.293333-37.973333s13.653333-30.336 30.293333-30.336z" fill="#3C78AA"></path><path d="M766.250667 298.666667v114.346666a121.6 121.6 0 0 1-121.6 121.984H420.693333A121.6 121.6 0 0 0 298.666667 656.597333v160a122.026667 122.026667 0 0 0 122.026666 122.026667h182.613334A122.026667 122.026667 0 0 0 725.333333 816.64v-71.68h-183.082666c-16.64 0-30.250667-24.32-30.250667-40.96h304.64A122.026667 122.026667 0 0 0 938.666667 581.973333v-161.28a122.026667 122.026667 0 0 0-122.026667-122.026666zM354.986667 491.221333l-0.170667 0.170667c0.512-0.085333 1.066667-0.042667 1.621333-0.170667z m279.04 310.442667c16.64 0 30.293333 12.8 30.293333 37.973333a30.293333 30.293333 0 0 1-30.293333 30.293334c-17.066667 0-30.72-5.12-30.72-30.293334s13.653333-37.973333 30.72-37.973333z" fill="#FDD835"></path></g></g></svg></div><div data-v-4d50615b="" class="attachment-block-card-info"><div data-v-4d50615b="" class="attachment-block-card-info-name">sync_monitor.py</div><div data-v-4d50615b="" class="attachment-block-card-info-size">19.14KB</div></div><div data-v-4d50615b="" class="attachment-block-card-preview"><div data-v-4d50615b="" class="attachment-block-card-preview-wrapper"><svg data-v-4d50615b="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="VisibleOutlined" class="attachment-block-card-preview-btn"><path d="M11.985 18.5c3.238 0 6.236-2.06 9.015-6.513C18.292 7.55 15.3 5.5 11.985 5.5 8.67 5.5 5.689 7.549 3 11.987c2.76 4.454 5.748 6.513 8.985 6.513ZM1.502 12.89a1.782 1.782 0 0 1 .023-1.838C4.428 6.017 7.915 3.5 11.984 3.5c4.086 0 7.594 2.538 10.523 7.614l.028.048c.296.519.294 1.16-.01 1.675-3.006 5.108-6.52 7.663-10.541 7.663-4.007 0-7.501-2.537-10.482-7.61ZM12 16a4 4 0 1 1 0-8 4 4 0 0 1 0 8Zm0-2a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" fill="rgb(100, 106, 115)"></path></svg></div></div><div data-v-4d50615b="" class="attachment-block-card-menu" style="display: none;"><div data-v-583c080e="" data-v-4d50615b="" class="attachment-menu"><ul data-v-583c080e="" class="attachment-menu-group"><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="MagnifyLeftOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M9 2a1 1 0 0 1 0 2H5.414l5.293 5.293a1 1 0 0 1-1.414 1.414L4 5.414V9a1 1 0 0 1-2 0V3a1 1 0 0 1 1-1h6Zm6 20a1 1 0 1 1 0-2h3.586l-5.293-5.293a1 1 0 0 1 1.414-1.414L20 18.586V15a1 1 0 1 1 2 0v6a1 1 0 0 1-1 1h-6Z" fill="rgb(43, 47, 54)"></path></svg></li><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="DownloadOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M20 18a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-2a1 1 0 1 1 2 0v1h14v-1a1 1 0 0 1 1-1Zm-7-3.964 2.657-2.657a1 1 0 0 1 1.414 1.414c-1.414 1.415-2.828 2.83-4.244 4.244a1 1 0 0 1-1.412 0c-1.417-1.415-2.833-2.833-4.249-4.25a.993.993 0 0 1 .013-1.401.992.992 0 0 1 1.401-.013l2.42 2.42V3.5a1 1 0 1 1 2 0v10.536Z" fill="rgb(43, 47, 54)"></path></svg></li></ul></div></div></div></div><blockquote><p data-v-550653ec="" class="paragraph"><strong>文件位置</strong> : <code>training/sync_monitor.py</code></p></blockquote><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.4.1 解决问题</strong></h4><p data-v-550653ec="" class="paragraph">训练监控模块的实现，解决的问题是实时监控训练过程中的系统资源使用情况，并生成详细的监控报告，满足比赛中提供监控报告的要求。</p><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.4.2 实现流程</strong></h4><ol start="1"><li><p data-v-550653ec="" class="paragraph">首先，因为监控器有配置信息（状态）和监控动作（行为），所以我们可以通过类来定义。</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">class</span><span style="color:#6F42C1"> UnifiedTrainingMonitor</span><span style="color:#24292EFF">:</span></span>
<span class="line"><span style="color:#D32F2F">    def</span><span style="color:#6F42C1"> __init__</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#D32F2F">        pass</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><ol start="2"><li><p data-v-550653ec="" class="paragraph">考虑到监控需要收集多种系统指标，那就在init初始化时设置监控配置，如果日志目录不存在则完成创建。</p></li><li><p data-v-550653ec="" class="paragraph">收集系统指标，有两个场景，一是实时显示监控信息，二是持久化保存，收集后要立即保存到文件；</p></li><li><p data-v-550653ec="" class="paragraph">通过循环每5秒执行一次监控；</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> _run_monitoring_loop</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#C2C3C5">    运行监控循环，显示实时信息并收集数据</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#D32F2F">    while</span><span style="color:#24292EFF"> self</span><span style="color:#212121">.</span><span style="color:#24292EFF">is_monitoring</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">        try</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#C2C3C5">            # 收集系统指标</span></span>
<span class="line"><span style="color:#24292EFF">            metrics </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> self</span><span style="color:#212121">.</span><span style="color:#6F42C1">_collect_all_metrics</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">            </span></span>
<span class="line"><span style="color:#C2C3C5">            # 存储数据点</span></span>
<span class="line"><span style="color:#24292EFF">            self</span><span style="color:#212121">.</span><span style="color:#24292EFF">data_points</span><span style="color:#212121">.</span><span style="color:#6F42C1">append</span><span style="color:#212121">(metrics)</span></span>
<span class="line"><span style="color:#24292EFF">            </span></span>
<span class="line"><span style="color:#C2C3C5">            # 记录到文件</span></span>
<span class="line"><span style="color:#24292EFF">            self</span><span style="color:#212121">.</span><span style="color:#6F42C1">_log_metrics_to_file</span><span style="color:#212121">(metrics)</span></span>
<span class="line"><span style="color:#24292EFF">            </span></span>
<span class="line"><span style="color:#C2C3C5">            # 显示实时信息</span></span>
<span class="line"><span style="color:#24292EFF">            self</span><span style="color:#212121">.</span><span style="color:#6F42C1">_display_realtime_info</span><span style="color:#212121">(metrics)</span></span>
<span class="line"><span style="color:#24292EFF">            </span></span>
<span class="line"><span style="color:#24292EFF">            time</span><span style="color:#212121">.</span><span style="color:#6F42C1">sleep</span><span style="color:#212121">(self.interval)</span></span>
<span class="line"><span style="color:#24292EFF">            </span></span>
<span class="line"><span style="color:#D32F2F">        except</span><span style="color:#1976D2"> Exception</span><span style="color:#D32F2F"> as</span><span style="color:#24292EFF"> e</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            logger</span><span style="color:#212121">.</span><span style="color:#6F42C1">error</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"监控过程中出错: </span><span style="color:#1976D2">{</span><span style="color:#212121">e</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">            time</span><span style="color:#212121">.</span><span style="color:#6F42C1">sleep</span><span style="color:#212121">(self.interval)</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><ol start="5"><li><p data-v-550653ec="" class="paragraph">合并流程：使用start_monitoring方法对监控流程做串联；</p></li></ol><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.4.3 运行效果</strong></h4><p data-v-550653ec="" class="paragraph"><strong>执行</strong> :</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 启动监控（建议在训练开始前启动）</span></span>
<span class="line"><span style="color:#6F42C1">python</span><span style="color:#2B5581"> training/sync_monitor.py</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 监控会自动：</span></span>
<span class="line"><span style="color:#C2C3C5"># 1. 检测GPU类型（NVIDIA/Apple Silicon）</span></span>
<span class="line"><span style="color:#C2C3C5"># 2. 查找训练相关进程</span></span>
<span class="line"><span style="color:#C2C3C5"># 3. 每5秒收集系统资源数据</span></span>
<span class="line"><span style="color:#C2C3C5"># 4. 实时显示监控信息</span></span>
<span class="line"><span style="color:#C2C3C5"># 5. 记录数据到JSONL文件</span></span>
<span class="line"><span style="color:#C2C3C5"># 6. 按Ctrl+C停止时生成报告</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>监控输出示例</strong> :</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#24292EFF">🚀 开始同步训练监控...</span></span>
<span class="line"><span style="color:#24292EFF">💡 提示</span><span style="color:#212121">:</span><span style="color:#24292EFF"> 按 Ctrl</span><span style="color:#D32F2F">+</span><span style="color:#24292EFF">C 停止监控</span></span>
<span class="line"><span style="color:#24292EFF">✅ 找到 </span><span style="color:#1976D2">2</span><span style="color:#24292EFF"> 个训练进程</span></span>
<span class="line"><span style="color:#24292EFF">🔍 监控中... </span><span style="color:#D32F2F">|</span><span style="color:#24292EFF"> CPU</span><span style="color:#212121">:</span><span style="color:#1976D2"> 45.2</span><span style="color:#D32F2F">%</span><span style="color:#D32F2F"> |</span><span style="color:#24292EFF"> 内存</span><span style="color:#212121">:</span><span style="color:#1976D2"> 67.8</span><span style="color:#D32F2F">%</span><span style="color:#24292EFF"> (</span><span style="color:#1976D2">24.</span><span style="color:#24292EFF">3GB</span><span style="color:#D32F2F">/</span><span style="color:#1976D2">36.</span><span style="color:#24292EFF">0GB) </span><span style="color:#D32F2F">|</span><span style="color:#24292EFF"> 磁盘</span><span style="color:#212121">:</span><span style="color:#1976D2"> 78.5</span><span style="color:#D32F2F">%</span><span style="color:#D32F2F"> |</span><span style="color:#24292EFF"> GPU</span><span style="color:#212121">:</span><span style="color:#1976D2"> 34.7</span><span style="color:#D32F2F">%</span><span style="color:#24292EFF"> (Apple M2 Pro) </span><span style="color:#D32F2F">|</span><span style="color:#24292EFF"> 训练进程</span><span style="color:#212121">:</span><span style="color:#1976D2"> 2</span><span style="color:#D32F2F"> |</span><span style="color:#24292EFF"> 数据点</span><span style="color:#212121">:</span><span style="color:#1976D2"> 156</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>生成的文件</strong> :</p><ul><li><p data-v-550653ec="" class="paragraph"><code>logs/training_monitor_data.jsonl</code> - 详细监控数据（JSONL格式）</p></li><li><p data-v-550653ec="" class="paragraph"><code>logs/training_resource_report.json</code> - 监控统计报告</p></li><li><p data-v-550653ec="" class="paragraph"><code>logs/sync_monitor.log</code> - 监控日志文件</p></li></ul><p data-v-550653ec="" class="paragraph"><strong>监控报告示例</strong> :</p><div data-v-4d50615b="" class="attachment-block-container"><div data-v-4d50615b="" class="attachment-block-card-container"><div data-v-4d50615b="" class="attachment-block-card-icon"><svg data-v-4d50615b="" width="48" height="48" viewBox="0 0 32 32" class="attachment-block-card-icon-svg"><defs><path d="M1.5 0h14.086a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V26.5a1.5 1.5 0 01-1.5 1.5h-19A1.5 1.5 0 010 26.5v-25A1.5 1.5 0 011.5 0z" id="file_json_svg__a"></path><path d="M16.293.293l5.414 5.414A1 1 0 0121.91 6H17.5A1.5 1.5 0 0116 4.5V.09a1 1 0 01.293.203z" id="file_json_svg__b"></path></defs><g fill="none" fill-rule="evenodd"><g transform="translate(5 2)"><use fill="#808080" xlink:href="#file_json_svg__a"></use><use fill="#666666" xlink:href="#file_json_svg__b"></use></g><g transform="translate(7 9) scale(0.017)"><path d="M28.672 563.2V460.8c22.528-2.048 39.936-3.072 50.176-10.24 10.24-3.072 19.456-13.312 27.648-24.576 8.192-12.288 13.312-25.6 17.408-45.056 2.048-13.312 5.12-35.84 5.12-68.608 0-55.296 2.048-93.184 8.192-115.712 5.12-20.48 15.36-39.936 27.648-52.224 13.312-12.288 34.816-22.528 60.416-30.72 17.408-3.072 45.056-8.192 84.992-8.192h24.576v100.352c-32.768 0-55.296 2.048-63.488 7.168-8.192 3.072-15.36 8.192-22.528 17.408-5.12 7.168-7.168 19.456-7.168 35.84 0 17.408-2.048 52.224-5.12 100.352-2.048 29.696-5.12 52.224-10.24 67.584-7.168 17.408-15.36 30.72-24.576 43.008-8.192 10.24-24.576 22.528-43.008 34.816 17.408 10.24 32.768 20.48 43.008 32.768 10.24 12.288 19.456 29.696 25.6 46.08 7.168 19.456 10.24 43.008 10.24 73.728 2.048 46.08 2.048 75.776 2.048 90.112 0 19.456 2.048 30.72 7.168 39.936 5.12 8.192 13.312 12.288 22.528 17.408 8.192 3.072 30.72 7.168 63.488 7.168v100.352H307.2c-39.936 0-72.704-2.048-91.136-8.192-22.528-7.168-39.936-17.408-55.296-30.72-15.36-13.312-24.576-30.72-30.72-52.224-5.12-20.48-7.168-53.248-7.168-98.304 0-52.224-2.048-86.016-7.168-100.352-7.168-22.528-17.408-39.936-30.72-50.176-8.192-10.24-29.696-16.384-56.32-16.384z m963.584 0c-22.528 2.048-39.936 3.072-50.176 10.24-10.24 3.072-19.456 13.312-27.648 24.576-8.192 12.288-13.312 25.6-17.408 45.056-2.048 13.312-5.12 35.84-5.12 68.608 0 55.296-2.048 93.184-8.192 115.712-5.12 22.528-15.36 39.936-27.648 52.224-13.312 12.288-34.816 22.528-60.416 30.72-17.408 3.072-45.056 8.192-84.992 8.192H686.08v-100.352c32.768 0 52.224-2.048 63.488-7.168 10.24-3.072 17.408-10.24 22.528-17.408 5.12-7.168 7.168-19.456 7.168-35.84s2.048-50.176 5.12-98.304c2.048-29.696 7.168-53.248 13.312-68.608 7.168-19.456 15.36-32.768 25.6-45.056 10.24-12.288 24.576-22.528 40.96-32.768-27.648-17.408-45.056-29.696-53.248-40.96-13.312-19.456-24.576-43.008-27.648-68.608-5.12-20.48-7.168-63.488-7.168-129.024 0-20.48-2.048-34.816-7.168-43.008-5.12-7.168-10.24-12.288-19.456-17.408-8.192-3.072-30.72-7.168-65.536-7.168v-100.352h24.576c39.936 0 72.704 2.048 91.136 8.192 22.528 7.168 39.936 17.408 55.296 30.72 15.36 13.312 24.576 30.72 30.72 52.224 5.12 20.48 8.192 53.248 8.192 98.304 0 52.224 2.048 84.992 7.168 100.352 7.168 22.528 17.408 39.936 30.72 46.08 13.312 10.24 34.816 13.312 60.416 17.408V563.2z m-289.792-10.24c-17.408-5.12-27.648-20.48-27.648-37.888 0-17.408 12.288-32.768 27.648-37.888 5.12-2.048 8.192-7.168 7.168-12.288-5.12-19.456-12.288-35.84-22.528-52.224-2.048-5.12-8.192-7.168-13.312-3.072-5.12 3.072-12.288 5.12-19.456 5.12-22.528 0-39.936-19.456-39.936-39.936 0-7.168 2.048-13.312 5.12-19.456 2.048-5.12 0-10.24-3.072-13.312-15.36-10.24-34.816-17.408-52.224-22.528-5.12-2.048-10.24 2.048-12.288 7.168-5.12 17.408-20.48 27.648-37.888 27.648-17.408 0-32.768-12.288-37.888-27.648-2.048-5.12-7.168-8.192-12.288-7.168-19.456 5.12-35.84 12.288-52.224 22.528-5.12 2.048-7.168 8.192-3.072 13.312 3.072 5.12 5.12 12.288 5.12 19.456 0 22.528-19.456 39.936-39.936 39.936-7.168 0-13.312-2.048-19.456-5.12-5.12-2.048-10.24 0-13.312 3.072-10.24 15.36-17.408 34.816-22.528 52.224-2.048 5.12 2.048 10.24 7.168 12.288 17.408 5.12 27.648 20.48 27.648 37.888 0 17.408-12.288 32.768-27.648 37.888-5.12 2.048-8.192 7.168-7.168 12.288 5.12 19.456 12.288 35.84 22.528 52.224 2.048 5.12 8.192 7.168 13.312 3.072 5.12-3.072 12.288-5.12 19.456-5.12 22.528 0 39.936 19.456 39.936 39.936 0 7.168-2.048 13.312-5.12 19.456-2.048 5.12 0 10.24 3.072 13.312 15.36 10.24 34.816 17.408 52.224 22.528h2.048c3.072 0 8.192-2.048 10.24-7.168 5.12-17.408 20.48-27.648 37.888-27.648 17.408 0 32.768 12.288 37.888 27.648 2.048 5.12 7.168 8.192 12.288 7.168 19.456-5.12 35.84-12.288 52.224-22.528 5.12-2.048 7.168-8.192 3.072-13.312-3.072-5.12-5.12-12.288-5.12-19.456 0-22.528 19.456-39.936 39.936-39.936 7.168 0 13.312 2.048 19.456 5.12 5.12 2.048 10.24 0 13.312-3.072 10.24-15.36 17.408-34.816 22.528-52.224 2.048-6.144-2.048-11.264-7.168-12.288z m-191.488 21.504c-34.816 0-62.464-27.648-62.464-62.464 0-34.816 27.648-62.464 62.464-62.464S573.44 477.184 573.44 512c0 35.84-27.648 62.464-62.464 62.464z" fill="#ffffff"></path></g></g></svg></div><div data-v-4d50615b="" class="attachment-block-card-info"><div data-v-4d50615b="" class="attachment-block-card-info-name">training_resource_report.json</div><div data-v-4d50615b="" class="attachment-block-card-info-size">0.79KB</div></div><div data-v-4d50615b="" class="attachment-block-card-preview"><div data-v-4d50615b="" class="attachment-block-card-preview-wrapper"><svg data-v-4d50615b="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="VisibleOutlined" class="attachment-block-card-preview-btn"><path d="M11.985 18.5c3.238 0 6.236-2.06 9.015-6.513C18.292 7.55 15.3 5.5 11.985 5.5 8.67 5.5 5.689 7.549 3 11.987c2.76 4.454 5.748 6.513 8.985 6.513ZM1.502 12.89a1.782 1.782 0 0 1 .023-1.838C4.428 6.017 7.915 3.5 11.984 3.5c4.086 0 7.594 2.538 10.523 7.614l.028.048c.296.519.294 1.16-.01 1.675-3.006 5.108-6.52 7.663-10.541 7.663-4.007 0-7.501-2.537-10.482-7.61ZM12 16a4 4 0 1 1 0-8 4 4 0 0 1 0 8Zm0-2a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" fill="rgb(100, 106, 115)"></path></svg></div></div><div data-v-4d50615b="" class="attachment-block-card-menu" style="display: none;"><div data-v-583c080e="" data-v-4d50615b="" class="attachment-menu"><ul data-v-583c080e="" class="attachment-menu-group"><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="MagnifyLeftOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M9 2a1 1 0 0 1 0 2H5.414l5.293 5.293a1 1 0 0 1-1.414 1.414L4 5.414V9a1 1 0 0 1-2 0V3a1 1 0 0 1 1-1h6Zm6 20a1 1 0 1 1 0-2h3.586l-5.293-5.293a1 1 0 0 1 1.414-1.414L20 18.586V15a1 1 0 1 1 2 0v6a1 1 0 0 1-1 1h-6Z" fill="rgb(43, 47, 54)"></path></svg></li><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="DownloadOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M20 18a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-2a1 1 0 1 1 2 0v1h14v-1a1 1 0 0 1 1-1Zm-7-3.964 2.657-2.657a1 1 0 0 1 1.414 1.414c-1.414 1.415-2.828 2.83-4.244 4.244a1 1 0 0 1-1.412 0c-1.417-1.415-2.833-2.833-4.249-4.25a.993.993 0 0 1 .013-1.401.992.992 0 0 1 1.401-.013l2.42 2.42V3.5a1 1 0 1 1 2 0v10.536Z" fill="rgb(43, 47, 54)"></path></svg></li></ul></div></div></div></div><p data-v-550653ec="" class="paragraph"></p><h3 class="heading"><strong>4.5 模型部署模块-Ollama最佳实践</strong></h3><h4 class="heading"><strong>4.5.1 模块概述</strong></h4><p data-v-550653ec="" class="paragraph">什么是Ollama？</p><p data-v-550653ec="" class="paragraph">Ollama是一个开源、免费、跨平台的大语言模型管理工具，它让我们能够轻松地在本地部署和管理各类开源大语言模型。与在线的ChatGPT、DeepSeek、文心一言等需要联网使用的模型不同，Ollama允许我们将开源模型下载到本地运行，无需申请API密钥，也不产生使用费用。</p><p data-v-550653ec="" class="paragraph">通过Ollama，我们可以将开源的大语言模型部署到本地，实现私有化的AI对话服务，特别适合企业内部知识库问答、个性化模型训练等场景。</p><p data-v-550653ec="" class="paragraph">模型部署模块就是负责将训练好的甄嬛LoRA模型部署到Ollama平台，实现本地化的AI对话服务，支持实时推理和多种应用接口。</p><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.5.2 核心实现步骤</strong></h4><h5 class="heading"><strong>1. 环境检查和安装</strong></h5><p data-v-550653ec="" class="paragraph"><strong>Ollama下载与安装</strong></p><p data-v-550653ec="" class="paragraph">首先，我们需要下载并安装Ollama。Ollama支持多种操作系统，你需要根据自己的系统选择合适的安装方式：</p><p data-v-550653ec="" class="paragraph"><strong>下载方式：</strong></p><ul><li><p data-v-550653ec="" class="paragraph">官网下载：访问https://ollama.ai，点击对应系统的下载按钮</p></li><li><p data-v-550653ec="" class="paragraph">GitHub下载：访问https://github.com/ollama/ollama获取最新版本</p></li></ul><p data-v-550653ec="" class="paragraph"><strong>系统要求：</strong></p><ul><li><p data-v-550653ec="" class="paragraph">磁盘空间：至少预留10GB空间（用于后续模型下载）</p></li><li><p data-v-550653ec="" class="paragraph">内存要求：根据模型大小而定</p><ul><li><p data-v-550653ec="" class="paragraph">7B模型：至少8GB内存</p></li><li><p data-v-550653ec="" class="paragraph">13B模型：至少16GB内存</p></li><li><p data-v-550653ec="" class="paragraph">33B模型：至少32GB内存</p></li></ul></li></ul><p data-v-550653ec="" class="paragraph"><strong>Windows安装步骤：</strong></p><ol start="1"><li><p data-v-550653ec="" class="paragraph">下载ollama-setup.exe安装包</p></li><li><p data-v-550653ec="" class="paragraph">双击运行安装程序</p></li><li><p data-v-550653ec="" class="paragraph">点击"Install"开始安装</p></li><li><p data-v-550653ec="" class="paragraph">安装完成后，系统托盘会显示Ollama图标</p></li></ol><p data-v-550653ec="" class="paragraph"><strong>macOS/Linux安装：</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># macOS</span></span>
<span class="line"><span style="color:#C2C3C5"># 安装homebrew </span></span>
<span class="line"><span style="color:#6F42C1">/bin/bash</span><span style="color:#2B5581"> -c</span><span style="color:#22863A"> "$(</span><span style="color:#6F42C1">curl</span><span style="color:#2B5581"> -fsSL</span><span style="color:#2B5581"> https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh</span><span style="color:#22863A">)"</span></span>
<span class="line"><span style="color:#C2C3C5"># 安装</span></span>
<span class="line"><span style="color:#6F42C1">brew</span><span style="color:#2B5581"> install</span><span style="color:#2B5581"> ollama</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># Linux 一键安装</span></span>
<span class="line"><span style="color:#6F42C1">curl</span><span style="color:#2B5581"> -fsSL</span><span style="color:#2B5581"> https://ollama.ai/install.sh</span><span style="color:#D32F2F"> |</span><span style="color:#6F42C1"> sh</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>验证安装：</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 检查 Ollama 安装</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> --version</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 查看帮助信息</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> --help</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>验证训练模型文件（确保训练已完成）：</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#6F42C1">ls</span><span style="color:#2B5581"> -la</span><span style="color:#2B5581"> ./training/training/models/huanhuan_fast/</span></span>
<span class="line"><span style="color:#C2C3C5"># 确认存在：adapter_config.json, adapter_model.safetensors</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>做模型转换</strong> ：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#6F42C1">python</span><span style="color:#2B5581"> llama.cpp/convert_lora_to_gguf.py</span><span style="color:#24292EFF"> \                                             </span></span>
<span class="line"><span style="color:#6F42C1">  training/models/huanhuan_fast</span><span style="color:#24292EFF"> \</span></span>
<span class="line"><span style="color:#2B5581">  --outfile</span><span style="color:#2B5581"> deployment/huanhuan_fast_lora.gguf</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>验证部署文件：</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#6F42C1">ls</span><span style="color:#2B5581"> -la</span><span style="color:#2B5581"> ./deployment/</span></span>
<span class="line"><span style="color:#C2C3C5"># 确认存在：Modelfile.huanhuan, huanhuan_fast_lora.gguf</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 653px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/e30fb887-a3a3-4a97-9f7e-abc8a54b071b.png" alt="图片"></div><!----></div><h5 class="heading"><strong>2. 启动Ollama服务</strong></h5><p data-v-550653ec="" class="paragraph">Ollama安装完成后会自动启动服务，但我们也需要了解如何手动管理服务状态。</p><p data-v-550653ec="" class="paragraph"><strong>自动启动：</strong></p><ul><li><p data-v-550653ec="" class="paragraph">Windows：安装完成后自动启动，系统托盘显示羊驼图标</p></li><li><p data-v-550653ec="" class="paragraph">macOS/Linux：安装后自动配置为系统服务</p></li></ul><p data-v-550653ec="" class="paragraph"><strong>手动启动服务：</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 启动 Ollama 服务（需要保持运行）</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> serve</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 1503px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/5af02237-1622-44b0-b049-24e1d1afa367.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph"><strong>验证服务状态：</strong></p><p data-v-550653ec="" class="paragraph">方法1：API接口验证</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 新开终端验证服务状态</span></span>
<span class="line"><span style="color:#6F42C1">curl</span><span style="color:#2B5581"> http://localhost:11434/api/tags</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 1506px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/e8144e3c-dab8-4d8c-b800-7a4359fa2be3.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph">方法2：查看已安装模型</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 列出所有已下载的模型</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> list</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph">方法3：检查服务进程</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># Windows</span></span>
<span class="line"><span style="color:#6F42C1">tasklist</span><span style="color:#D32F2F"> |</span><span style="color:#6F42C1"> findstr</span><span style="color:#2B5581"> ollama</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># macOS/Linux</span></span>
<span class="line"><span style="color:#6F42C1">ps</span><span style="color:#2B5581"> aux</span><span style="color:#D32F2F"> |</span><span style="color:#6F42C1"> grep</span><span style="color:#2B5581"> ollama</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>常见问题解决：</strong></p><p data-v-550653ec="" class="paragraph">如果11434端口被占用，可以指定其他端口：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 指定端口启动</span></span>
<span class="line"><span style="color:#24292EFF">OLLAMA_HOST</span><span style="color:#D32F2F">=</span><span style="color:#2B5581">0.0.0.0:11435</span><span style="color:#6F42C1"> ollama</span><span style="color:#2B5581"> serve</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><h5 class="heading">3. 拉取基础模型</h5><p data-v-550653ec="" class="paragraph">Ollama支持多种类型的开源大语言模型，我们可以通过访问https://ollama.ai/library查看所有可用模型。</p><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 942px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/7c60d266-d86c-4f14-8d7d-96d67beb5232.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph"><strong>模型分类：</strong></p><div class="tableWrapper"><table><colgroup><col style="width: 100px;"></colgroup><tbody><tr><td colspan="1" rowspan="1" colwidth="100"><p data-v-550653ec="" class="paragraph"><strong>模型类别</strong></p></td><td colspan="1" rowspan="1" colwidth="173"><p data-v-550653ec="" class="paragraph"><strong>作用</strong></p></td><td colspan="1" rowspan="1" colwidth="100"><p data-v-550653ec="" class="paragraph"><strong>具体模型</strong></p></td><td colspan="1" rowspan="1" colwidth="329"><p data-v-550653ec="" class="paragraph"><strong>具体模型内容</strong></p></td></tr><tr><td colspan="1" rowspan="4" colwidth="100"><p data-v-550653ec="" class="paragraph">语言类模型</p><p data-v-550653ec="" class="paragraph"> </p></td><td colspan="1" rowspan="4" colwidth="173"><p data-v-550653ec="" class="paragraph">用于文本对话</p><p data-v-550653ec="" class="paragraph"> </p></td><td colspan="1" rowspan="1" colwidth="100"><p data-v-550653ec="" class="paragraph">Qwen</p></td><td colspan="1" rowspan="1" colwidth="329"><p data-v-550653ec="" class="paragraph">阿里云开发的中文友好模型</p></td></tr><tr><td colspan="1" rowspan="1" colwidth="100"><p data-v-550653ec="" class="paragraph">DeepSeek</p></td><td colspan="1" rowspan="1" colwidth="329"><p data-v-550653ec="" class="paragraph">深度求索开发的高性能模型</p></td></tr><tr><td colspan="1" rowspan="1" colwidth="100"><p data-v-550653ec="" class="paragraph">Gemma</p></td><td colspan="1" rowspan="1" colwidth="329"><p data-v-550653ec="" class="paragraph">Google开发的轻量级模型</p></td></tr><tr><td colspan="1" rowspan="1" colwidth="100"><p data-v-550653ec="" class="paragraph">Llama</p></td><td colspan="1" rowspan="1" colwidth="329"><p data-v-550653ec="" class="paragraph">Meta开发的经典模型系列</p></td></tr><tr><td colspan="1" rowspan="2" colwidth="100"><p data-v-550653ec="" class="paragraph">视觉类模型</p></td><td colspan="1" rowspan="2" colwidth="173"><p data-v-550653ec="" class="paragraph">支持图片识别</p></td><td colspan="1" rowspan="1" colwidth="100"><p data-v-550653ec="" class="paragraph">LLaVA</p><p data-v-550653ec="" class="paragraph"> </p></td><td colspan="1" rowspan="1" colwidth="329"><p data-v-550653ec="" class="paragraph">专门用于图片理解和描述</p></td></tr><tr><td colspan="1" rowspan="1" colwidth="100"><p data-v-550653ec="" class="paragraph">Mini-CPM-V</p></td><td colspan="1" rowspan="1" colwidth="329"><p data-v-550653ec="" class="paragraph">轻量级多模态模型</p></td></tr><tr><td colspan="1" rowspan="1" colwidth="100"><p data-v-550653ec="" class="paragraph">嵌入模型</p></td><td colspan="1" rowspan="1" colwidth="173"><p data-v-550653ec="" class="paragraph">用于文本向量化</p></td><td colspan="1" rowspan="1" colwidth="100"><p data-v-550653ec="" class="paragraph">nomic-embed-text</p></td><td colspan="1" rowspan="1" colwidth="329"><p data-v-550653ec="" class="paragraph">用于文本嵌入和相似度计算</p></td></tr></tbody></table></div><p data-v-550653ec="" class="paragraph"></p><p data-v-550653ec="" class="paragraph"><strong>模型大小与参数量对应关系：</strong></p><blockquote><p data-v-550653ec="" class="paragraph">https://ollama.com/library/deepseek-r1</p></blockquote><ul><li><p data-v-550653ec="" class="paragraph">1.5B → 约1.1GB磁盘空间</p></li><li><p data-v-550653ec="" class="paragraph">7B → 约4.7GB磁盘空间</p></li><li><p data-v-550653ec="" class="paragraph">13B → 约8GB磁盘空间</p></li><li><p data-v-550653ec="" class="paragraph">33B → 约20GB磁盘空间</p></li><li><p data-v-550653ec="" class="paragraph">70B → 约40GB磁盘空间</p></li></ul><p data-v-550653ec="" class="paragraph">注：B表示模型参数量（Billion），参数越多模型越大，回答质量通常越好，但对硬件要求也越高。</p><p data-v-550653ec="" class="paragraph"></p><p data-v-550653ec="" class="paragraph"><strong>下载基础模型：</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 检查现有模型</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> list</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 拉取 Qwen2.5-0.5B 基础模型（项目使用）</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> pull</span><span style="color:#2B5581"> qwen2.5:0.5b</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 验证基础模型下载成功</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> list</span><span style="color:#D32F2F"> |</span><span style="color:#6F42C1"> grep</span><span style="color:#2B5581"> qwen2.5</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><h5 class="heading"><strong>4. 创建Modelfile配置</strong></h5><blockquote><p data-v-550653ec="" class="paragraph"><strong>文件位置</strong> : <code>deployment/Modelfile.huanhuan</code></p></blockquote><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><h5 class="heading"><strong>5. LoRA适配器适配gguf</strong></h5><p data-v-550653ec="" class="paragraph">项目已包含预转换的GGUF格式适配器文件：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 验证适配器文件存在</span></span>
<span class="line"><span style="color:#6F42C1">ls</span><span style="color:#2B5581"> -la</span><span style="color:#2B5581"> deployment/huanhuan_fast_lora.gguf</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 文件大小约为几百KB，包含LoRA权重</span></span>
<span class="line"><span style="color:#C2C3C5"># 注意：此文件已经是GGUF格式，无需额外转换</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 1524px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/dba7a7a3-9736-45e3-9ff9-e5be4a7d1666.png" alt="图片"></div><!----></div><h5 class="heading"><strong>6. 创建Ollama模型</strong></h5><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 进入deployment目录</span></span>
<span class="line"><span style="color:#6F42C1">cd</span><span style="color:#2B5581"> deployment</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 使用现有Modelfile创建ollama模型</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> create</span><span style="color:#2B5581"> huanhuan-qwen</span><span style="color:#2B5581"> -f</span><span style="color:#2B5581"> Modelfile.huanhuan</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 验证模型创建成功</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> list</span><span style="color:#D32F2F"> |</span><span style="color:#6F42C1"> grep</span><span style="color:#2B5581"> huanhuan</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 936px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/ea3a4b28-9186-4f72-8140-cedb4b94628c.png" alt="图片"></div><!----></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 664px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/d3644ac1-de43-4e2c-add2-ed3b6eaba50e.png" alt="图片"></div><!----></div><h5 class="heading"><strong>7. 模型验证和测试</strong></h5><p data-v-550653ec="" class="paragraph">创建模型后，我们需要验证模型是否正常工作，并了解各种使用方式。</p><p data-v-550653ec="" class="paragraph"><strong>查看模型信息：</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 检查模型详细信息</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> show</span><span style="color:#2B5581"> huanhuan-qwen</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 显示内容包括：模型架构、参数量、模板格式等</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 1356px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/3153e4db-b884-4090-81c5-248f7c2b1e3f.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph"><strong>交互式测试：</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 启动交互式对话</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> run</span><span style="color:#2B5581"> huanhuan-qwen</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 进入对话模式后可以：</span></span>
<span class="line"><span style="color:#C2C3C5"># - 直接输入问题进行对话</span></span>
<span class="line"><span style="color:#C2C3C5"># - 使用 Ctrl+D 或 /bye 退出</span></span>
<span class="line"><span style="color:#C2C3C5"># - 使用 /clear 清除上下文</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>命令行直接对话：</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 单次问答（不进入交互模式）</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> run</span><span style="color:#2B5581"> huanhuan-qwen</span><span style="color:#22863A"> "你是谁？"</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 多行输入</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> run</span><span style="color:#2B5581"> huanhuan-qwen</span><span style="color:#22863A"> """</span></span>
<span class="line"><span style="color:#22863A">给我讲个故事，</span></span>
<span class="line"><span style="color:#22863A">要求包含：</span></span>
<span class="line"><span style="color:#22863A">1. 主角是甄嬛</span></span>
<span class="line"><span style="color:#22863A">2. 故事简短</span></span>
<span class="line"><span style="color:#22863A">"""</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 2025px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/54d753b7-1598-4347-b3ea-ea5ecf5bbfb7.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph"><strong>性能测试：</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 查看模型执行效率详情</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> run</span><span style="color:#2B5581"> huanhuan-qwen</span><span style="color:#2B5581"> --verbose</span><span style="color:#22863A"> "介绍一下你自己"</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 输出信息包括：</span></span>
<span class="line"><span style="color:#C2C3C5"># - total duration: 总运行时间</span></span>
<span class="line"><span style="color:#C2C3C5"># - load duration: 模型加载时间</span></span>
<span class="line"><span style="color:#C2C3C5"># - prompt eval count: 提示词token数量</span></span>
<span class="line"><span style="color:#C2C3C5"># - prompt eval duration: 提示词处理时间</span></span>
<span class="line"><span style="color:#C2C3C5"># - eval count: 响应token数量</span></span>
<span class="line"><span style="color:#C2C3C5"># - eval duration: 响应生成时间</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 2025px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/e98f2daf-de75-4517-9928-853ca15ac57e.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph"><strong>多模态测试（如果使用支持图片的模型，比如llava:7b）：</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 图片识别测试（需要LLaVA等视觉模型）</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> run</span><span style="color:#2B5581"> llava:7b</span><span style="color:#22863A"> "描述这张图片的内容"</span><span style="color:#2B5581"> /path/to/image.jpg</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>常用Ollama命令总结：</strong></p><ul><li><p data-v-550653ec="" class="paragraph">模型管理命令：</p></li></ul><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> list</span><span style="color:#C2C3C5">                    # 列出所有已下载模型</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> pull</span><span style="color:#D32F2F"> &lt;</span><span style="color:#2B5581">mode</span><span style="color:#24292EFF">l</span><span style="color:#D32F2F">&gt;</span><span style="color:#C2C3C5">           # 下载/更新模型</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> rm</span><span style="color:#D32F2F"> &lt;</span><span style="color:#2B5581">mode</span><span style="color:#24292EFF">l</span><span style="color:#D32F2F">&gt;</span><span style="color:#C2C3C5">             # 删除模型</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> show</span><span style="color:#D32F2F"> &lt;</span><span style="color:#2B5581">mode</span><span style="color:#24292EFF">l</span><span style="color:#D32F2F">&gt;</span><span style="color:#C2C3C5">           # 显示模型信息</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><ul><li><p data-v-550653ec="" class="paragraph">模型运行命令：</p></li></ul><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> run</span><span style="color:#D32F2F"> &lt;</span><span style="color:#2B5581">mode</span><span style="color:#24292EFF">l</span><span style="color:#D32F2F">&gt;</span><span style="color:#C2C3C5">            # 交互式运行</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> run</span><span style="color:#D32F2F"> &lt;</span><span style="color:#2B5581">mode</span><span style="color:#24292EFF">l</span><span style="color:#D32F2F">&gt;</span><span style="color:#22863A"> "问题"</span><span style="color:#C2C3C5">     # 单次问答</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> run</span><span style="color:#D32F2F"> &lt;</span><span style="color:#2B5581">mode</span><span style="color:#24292EFF">l</span><span style="color:#D32F2F">&gt;</span><span style="color:#2B5581"> --verbose</span><span style="color:#C2C3C5">  # 显示性能信息</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><ul><li><p data-v-550653ec="" class="paragraph">服务管理命令：</p></li></ul><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> serve</span><span style="color:#C2C3C5">                  # 启动服务</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> create</span><span style="color:#D32F2F"> &lt;</span><span style="color:#2B5581">nam</span><span style="color:#24292EFF">e</span><span style="color:#D32F2F">&gt;</span><span style="color:#2B5581"> -f</span><span style="color:#D32F2F"> &lt;</span><span style="color:#2B5581">fil</span><span style="color:#24292EFF">e</span><span style="color:#D32F2F">&gt;</span><span style="color:#C2C3C5"> # 创建自定义模型</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.5.3 常见问题处理</strong></h4><ul><li><p data-v-550653ec="" class="paragraph">使用Modelfile创建ollama模型出错</p></li></ul><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#24292EFF">ollama create huanhuan</span><span style="color:#D32F2F">-</span><span style="color:#24292EFF">qwen </span><span style="color:#D32F2F">-</span><span style="color:#24292EFF">f deployment</span><span style="color:#D32F2F">/</span><span style="color:#24292EFF">Modelfile</span><span style="color:#212121">.</span><span style="color:#24292EFF">huanhuan</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph">如果这时直接使用，会提示出错。</p><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 820px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/5f2ecdac-de2c-457c-bcf5-86460b15a21e.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph">这是因为LoRA适配器的架构和基础模型qwen2.5:0.5b不兼容，需要用GGUF格式的适配器文件，适配器是为不同的模型架构训练的。</p><p data-v-550653ec="" class="paragraph"></p><h3 class="heading"><strong>4.6 Web应用模块-Streamlit最佳实践</strong></h3><div data-v-4d50615b="" class="attachment-block-container"><div data-v-4d50615b="" class="attachment-block-card-container"><div data-v-4d50615b="" class="attachment-block-card-icon"><svg data-v-4d50615b="" width="48" height="48" viewBox="0 0 32 32" class="attachment-block-card-icon-svg"><defs><path d="M1.5 0h14.086a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V26.5a1.5 1.5 0 01-1.5 1.5h-19A1.5 1.5 0 010 26.5v-25A1.5 1.5 0 011.5 0z" id="icon_file_js_svg__a"></path><path d="M16.293.293l5.414 5.414A1 1 0 0121.91 6H17.5A1.5 1.5 0 0116 4.5V.09a1 1 0 01.293.203z" id="icon_file_js_svg__b"></path></defs><g fill="none" fill-rule="evenodd"><g transform="translate(5 2)"><use fill="#eef" xlink:href="#icon_file_js_svg__a"></use><use fill="#ccc" xlink:href="#icon_file_js_svg__b"></use></g><g transform="translate(7 7) scale(0.019)"><path d="M420.693333 85.333333C353.28 85.333333 298.666667 139.946667 298.666667 207.36v71.68h183.04c16.64 0 30.293333 24.32 30.293333 40.96H207.36C139.946667 320 85.333333 374.613333 85.333333 442.026667v161.322666c0 67.413333 54.613333 122.026667 122.026667 122.026667h50.346667v-114.346667c0-67.413333 54.186667-122.026667 121.6-122.026666h224c67.413333 0 122.026667-54.229333 122.026666-121.642667V207.36C725.333333 139.946667 670.72 85.333333 603.306667 85.333333z m-30.72 68.693334c17.066667 0 30.72 5.12 30.72 30.293333s-13.653333 38.016-30.72 38.016c-16.64 0-30.293333-12.8-30.293333-37.973333s13.653333-30.336 30.293333-30.336z" fill="#3C78AA"></path><path d="M766.250667 298.666667v114.346666a121.6 121.6 0 0 1-121.6 121.984H420.693333A121.6 121.6 0 0 0 298.666667 656.597333v160a122.026667 122.026667 0 0 0 122.026666 122.026667h182.613334A122.026667 122.026667 0 0 0 725.333333 816.64v-71.68h-183.082666c-16.64 0-30.250667-24.32-30.250667-40.96h304.64A122.026667 122.026667 0 0 0 938.666667 581.973333v-161.28a122.026667 122.026667 0 0 0-122.026667-122.026666zM354.986667 491.221333l-0.170667 0.170667c0.512-0.085333 1.066667-0.042667 1.621333-0.170667z m279.04 310.442667c16.64 0 30.293333 12.8 30.293333 37.973333a30.293333 30.293333 0 0 1-30.293333 30.293334c-17.066667 0-30.72-5.12-30.72-30.293334s13.653333-37.973333 30.72-37.973333z" fill="#FDD835"></path></g></g></svg></div><div data-v-4d50615b="" class="attachment-block-card-info"><div data-v-4d50615b="" class="attachment-block-card-info-name">huanhuan_web.py</div><div data-v-4d50615b="" class="attachment-block-card-info-size">17.61KB</div></div><div data-v-4d50615b="" class="attachment-block-card-preview"><div data-v-4d50615b="" class="attachment-block-card-preview-wrapper"><svg data-v-4d50615b="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="VisibleOutlined" class="attachment-block-card-preview-btn"><path d="M11.985 18.5c3.238 0 6.236-2.06 9.015-6.513C18.292 7.55 15.3 5.5 11.985 5.5 8.67 5.5 5.689 7.549 3 11.987c2.76 4.454 5.748 6.513 8.985 6.513ZM1.502 12.89a1.782 1.782 0 0 1 .023-1.838C4.428 6.017 7.915 3.5 11.984 3.5c4.086 0 7.594 2.538 10.523 7.614l.028.048c.296.519.294 1.16-.01 1.675-3.006 5.108-6.52 7.663-10.541 7.663-4.007 0-7.501-2.537-10.482-7.61ZM12 16a4 4 0 1 1 0-8 4 4 0 0 1 0 8Zm0-2a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" fill="rgb(100, 106, 115)"></path></svg></div></div><div data-v-4d50615b="" class="attachment-block-card-menu" style="display: none;"><div data-v-583c080e="" data-v-4d50615b="" class="attachment-menu"><ul data-v-583c080e="" class="attachment-menu-group"><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="MagnifyLeftOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M9 2a1 1 0 0 1 0 2H5.414l5.293 5.293a1 1 0 0 1-1.414 1.414L4 5.414V9a1 1 0 0 1-2 0V3a1 1 0 0 1 1-1h6Zm6 20a1 1 0 1 1 0-2h3.586l-5.293-5.293a1 1 0 0 1 1.414-1.414L20 18.586V15a1 1 0 1 1 2 0v6a1 1 0 0 1-1 1h-6Z" fill="rgb(43, 47, 54)"></path></svg></li><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="DownloadOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M20 18a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-2a1 1 0 1 1 2 0v1h14v-1a1 1 0 0 1 1-1Zm-7-3.964 2.657-2.657a1 1 0 0 1 1.414 1.414c-1.414 1.415-2.828 2.83-4.244 4.244a1 1 0 0 1-1.412 0c-1.417-1.415-2.833-2.833-4.249-4.25a.993.993 0 0 1 .013-1.401.992.992 0 0 1 1.401-.013l2.42 2.42V3.5a1 1 0 1 1 2 0v10.536Z" fill="rgb(43, 47, 54)"></path></svg></li></ul></div></div></div></div><h4 class="heading"></h4><h4 class="heading"><strong>4.6.1 模块概述</strong></h4><p data-v-550653ec="" class="paragraph">基于Streamlit构建的甄嬛角色对话Web应用，提供友好的用户界面和实时对话功能。支持模型选择、流式对话、连接状态监控、参数调节、对话历史管理等完整功能。</p><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.6.2 Streamlit概述</strong></h4><p data-v-550653ec="" class="paragraph">Streamlit是一个基于Python的开源框架，专门用于快速构建数据科学和机器学习的Web应用，无需前端开发（html、css、js）经验，只使用纯Python代码就能构建美观的Web应用。</p><h5 class="heading"><strong>安装和基础使用</strong></h5><p data-v-550653ec="" class="paragraph">首先安装Streamlit：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#6F42C1">pip</span><span style="color:#2B5581"> install</span><span style="color:#2B5581"> streamlit</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph">创建第一个Streamlit应用：app.py</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> streamlit </span><span style="color:#D32F2F">as</span><span style="color:#24292EFF"> st</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 设置页面标题</span></span>
<span class="line"><span style="color:#24292EFF">st</span><span style="color:#212121">.</span><span style="color:#6F42C1">title</span><span style="color:#212121">(</span><span style="color:#22863A">"我的第一个Streamlit应用"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 显示文本</span></span>
<span class="line"><span style="color:#24292EFF">st</span><span style="color:#212121">.</span><span style="color:#6F42C1">write</span><span style="color:#212121">(</span><span style="color:#22863A">"Hello, Streamlit!"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 显示数据</span></span>
<span class="line"><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> pandas </span><span style="color:#D32F2F">as</span><span style="color:#24292EFF"> pd</span></span>
<span class="line"><span style="color:#24292EFF">df </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> pd</span><span style="color:#212121">.</span><span style="color:#6F42C1">DataFrame</span><span style="color:#212121">({</span></span>
<span class="line"><span style="color:#22863A">    '姓名'</span><span style="color:#212121">: [</span><span style="color:#22863A">'张三'</span><span style="color:#212121">, </span><span style="color:#22863A">'李四'</span><span style="color:#212121">, </span><span style="color:#22863A">'王五'</span><span style="color:#212121">],</span></span>
<span class="line"><span style="color:#22863A">    '年龄'</span><span style="color:#212121">: [</span><span style="color:#1976D2">25</span><span style="color:#212121">, </span><span style="color:#1976D2">30</span><span style="color:#212121">, </span><span style="color:#1976D2">35</span><span style="color:#212121">]</span></span>
<span class="line"><span style="color:#212121">})</span></span>
<span class="line"><span style="color:#24292EFF">st</span><span style="color:#212121">.</span><span style="color:#6F42C1">dataframe</span><span style="color:#212121">(df)</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph">运行应用：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#6F42C1">streamlit</span><span style="color:#2B5581"> run</span><span style="color:#2B5581"> app.py</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph">浏览器会自动打开http://localhost:8501显示你的应用。</p><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 1606px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/8b0e388c-77b2-4e9f-adf2-857e04de990a.png" alt="图片"></div><!----></div><h5 class="heading"><strong>Streamlit基础组件</strong></h5><blockquote><p data-v-550653ec="" class="paragraph">通过这些基础组件的组合，来完成我们想要的功能。</p></blockquote><ul><li><p data-v-550653ec="" class="paragraph">自动渲染机制</p></li></ul><p data-v-550653ec="" class="paragraph">Streamlit有一个重要特性： <strong>每次用户交互都会重新执行整个Python脚本</strong> 。这意味着：用户点击按钮、输入文本等操作，变量会被重新初始化，会触发脚本重新运行。也就是说，刚刚我们执行的： <code>streamlit run app.py</code> 不需要变动，只需要改变代码，就能实时的调整界面。但是对于聊天对话的任务，就需要使用 <code>st.session_state</code> 来保持状态（下面会说到），避免频繁刷新改变。</p><ul><li><p data-v-550653ec="" class="paragraph">文本和标题组件</p></li></ul><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> streamlit </span><span style="color:#D32F2F">as</span><span style="color:#24292EFF"> st</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 不同级别的标题</span></span>
<span class="line"><span style="color:#24292EFF">st</span><span style="color:#212121">.</span><span style="color:#6F42C1">title</span><span style="color:#212121">(</span><span style="color:#22863A">"主标题"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">st</span><span style="color:#212121">.</span><span style="color:#6F42C1">header</span><span style="color:#212121">(</span><span style="color:#22863A">"二级标题"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">st</span><span style="color:#212121">.</span><span style="color:#6F42C1">subheader</span><span style="color:#212121">(</span><span style="color:#22863A">"三级标题"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 文本显示</span></span>
<span class="line"><span style="color:#24292EFF">st</span><span style="color:#212121">.</span><span style="color:#6F42C1">text</span><span style="color:#212121">(</span><span style="color:#22863A">"普通文本"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(</span><span style="color:#22863A">"**粗体文本** 和 *斜体文本*"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">st</span><span style="color:#212121">.</span><span style="color:#6F42C1">write</span><span style="color:#212121">(</span><span style="color:#22863A">"万能显示函数，自动识别数据类型"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 代码显示</span></span>
<span class="line"><span style="color:#24292EFF">st</span><span style="color:#212121">.</span><span style="color:#6F42C1">code</span><span style="color:#212121">(</span><span style="color:#22863A">"""</span></span>
<span class="line"><span style="color:#22863A">def hello():</span></span>
<span class="line"><span style="color:#22863A">    print("Hello, World!")</span></span>
<span class="line"><span style="color:#22863A">"""</span><span style="color:#212121">, language</span><span style="color:#D32F2F">=</span><span style="color:#22863A">'python'</span><span style="color:#212121">)</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 750px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/62eef7f4-8f5e-494c-ba0a-d62d4a5ff2f9.png" alt="图片"></div><!----></div><ul><li><p data-v-550653ec="" class="paragraph">输入组件</p></li></ul><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> streamlit </span><span style="color:#D32F2F">as</span><span style="color:#24292EFF"> st</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 文本输入</span></span>
<span class="line"><span style="color:#24292EFF">name </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">text_input</span><span style="color:#212121">(</span><span style="color:#22863A">"请输入您的姓名"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 选择框</span></span>
<span class="line"><span style="color:#24292EFF">option </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">selectbox</span><span style="color:#212121">(</span></span>
<span class="line"><span style="color:#22863A">    "选择您喜欢的颜色"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">    [</span><span style="color:#22863A">"红色"</span><span style="color:#212121">, </span><span style="color:#22863A">"绿色"</span><span style="color:#212121">, </span><span style="color:#22863A">"蓝色"</span><span style="color:#212121">]</span></span>
<span class="line"><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 滑块</span></span>
<span class="line"><span style="color:#24292EFF">age </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">slider</span><span style="color:#212121">(</span><span style="color:#22863A">"选择年龄"</span><span style="color:#212121">, </span><span style="color:#1976D2">0</span><span style="color:#212121">, </span><span style="color:#1976D2">100</span><span style="color:#212121">, </span><span style="color:#1976D2">25</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 按钮</span></span>
<span class="line"><span style="color:#D32F2F">if</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">button</span><span style="color:#212121">(</span><span style="color:#22863A">"提交"</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">write</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"您好 </span><span style="color:#1976D2">{</span><span style="color:#212121">name</span><span style="color:#1976D2">}</span><span style="color:#22863A">，您选择了 </span><span style="color:#1976D2">{</span><span style="color:#212121">option</span><span style="color:#1976D2">}</span><span style="color:#22863A">，年龄是 </span><span style="color:#1976D2">{</span><span style="color:#212121">age</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">)</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 892px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/1f152c40-4bd5-471d-81b5-04456abbc20b.png" alt="图片"></div><!----></div><ul><li><p data-v-550653ec="" class="paragraph">session_state状态管理</p></li></ul><p data-v-550653ec="" class="paragraph">由于Streamlit每次交互都重新执行脚本，我们需要 <code>st.session_state</code> 来保持数据：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> streamlit </span><span style="color:#D32F2F">as</span><span style="color:#24292EFF"> st</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 初始化计数器</span></span>
<span class="line"><span style="color:#D32F2F">if</span><span style="color:#22863A"> 'count'</span><span style="color:#D32F2F"> not</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">count </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> 0</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 显示当前计数</span></span>
<span class="line"><span style="color:#24292EFF">st</span><span style="color:#212121">.</span><span style="color:#6F42C1">write</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"当前计数: </span><span style="color:#1976D2">{</span><span style="color:#212121">st.session_state.count</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 增加计数的按钮</span></span>
<span class="line"><span style="color:#D32F2F">if</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">button</span><span style="color:#212121">(</span><span style="color:#22863A">"点击增加"</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">count </span><span style="color:#D32F2F">+=</span><span style="color:#1976D2"> 1</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">rerun</span><span style="color:#212121">()</span><span style="color:#C2C3C5">  # 重新运行以更新显示</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph">当点击这个button后，实际程序才会运行添加。</p><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 854px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/22edc2fe-3b6d-41ad-838e-d00dac876c87.png" alt="图片"></div><!----></div><ul><li><p data-v-550653ec="" class="paragraph">聊天界面组件</p></li></ul><p data-v-550653ec="" class="paragraph">Streamlit提供了专门的聊天组件，非常适合构建对话应用：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> streamlit </span><span style="color:#D32F2F">as</span><span style="color:#24292EFF"> st</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 初始化消息历史</span></span>
<span class="line"><span style="color:#D32F2F">if</span><span style="color:#22863A"> "messages"</span><span style="color:#D32F2F"> not</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">messages </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> []</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 显示聊天历史</span></span>
<span class="line"><span style="color:#D32F2F">for</span><span style="color:#24292EFF"> message </span><span style="color:#D32F2F">in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">messages</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">    with</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">chat_message</span><span style="color:#212121">(message[</span><span style="color:#22863A">"role"</span><span style="color:#212121">]):</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(message[</span><span style="color:#22863A">"content"</span><span style="color:#212121">])</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 聊天输入框</span></span>
<span class="line"><span style="color:#D32F2F">if</span><span style="color:#24292EFF"> prompt </span><span style="color:#D32F2F">:=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">chat_input</span><span style="color:#212121">(</span><span style="color:#22863A">"请输入您的消息"</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#C2C3C5">    # 添加用户消息</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">messages</span><span style="color:#212121">.</span><span style="color:#6F42C1">append</span><span style="color:#212121">({</span><span style="color:#22863A">"role"</span><span style="color:#212121">: </span><span style="color:#22863A">"user"</span><span style="color:#212121">, </span><span style="color:#22863A">"content"</span><span style="color:#212121">: prompt})</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    with</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">chat_message</span><span style="color:#212121">(</span><span style="color:#22863A">"user"</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(prompt)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 模拟AI回复</span></span>
<span class="line"><span style="color:#24292EFF">    response </span><span style="color:#D32F2F">=</span><span style="color:#D32F2F"> f</span><span style="color:#22863A">"您说了: </span><span style="color:#1976D2">{</span><span style="color:#24292EFF">prompt</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    with</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">chat_message</span><span style="color:#212121">(</span><span style="color:#22863A">"assistant"</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(response)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 添加助手消息</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">messages</span><span style="color:#212121">.</span><span style="color:#6F42C1">append</span><span style="color:#212121">({</span><span style="color:#22863A">"role"</span><span style="color:#212121">: </span><span style="color:#22863A">"assistant"</span><span style="color:#212121">, </span><span style="color:#22863A">"content"</span><span style="color:#212121">: response})</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 812px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/4561f02b-cb7f-4986-951f-6bc0b53054a2.png" alt="图片"></div><!----></div><ul><li><p data-v-550653ec="" class="paragraph">页面布局-侧边栏</p></li></ul><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">with</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">sidebar</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">title</span><span style="color:#212121">(</span><span style="color:#22863A">"设置"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    temperature </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">slider</span><span style="color:#212121">(</span><span style="color:#22863A">"Temperature"</span><span style="color:#212121">, </span><span style="color:#1976D2">0.0</span><span style="color:#212121">, </span><span style="color:#1976D2">2.0</span><span style="color:#212121">, </span><span style="color:#1976D2">0.7</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    model </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">selectbox</span><span style="color:#212121">(</span><span style="color:#22863A">"选择模型"</span><span style="color:#212121">, [</span><span style="color:#22863A">"GPT-3.5"</span><span style="color:#212121">, </span><span style="color:#22863A">"GPT-4"</span><span style="color:#212121">])</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 538px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/bf522e30-ecda-49e4-885c-092913fd2dac.png" alt="图片"></div><!----></div><ul><li><p data-v-550653ec="" class="paragraph">页面布局-多列</p></li></ul><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 创建三列</span></span>
<span class="line"><span style="color:#24292EFF">col1</span><span style="color:#212121">,</span><span style="color:#24292EFF"> col2</span><span style="color:#212121">,</span><span style="color:#24292EFF"> col3 </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">columns</span><span style="color:#212121">(</span><span style="color:#1976D2">3</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#D32F2F">with</span><span style="color:#24292EFF"> col1</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">write</span><span style="color:#212121">(</span><span style="color:#22863A">"第一列内容"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#D32F2F">with</span><span style="color:#24292EFF"> col2</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">write</span><span style="color:#212121">(</span><span style="color:#22863A">"第二列内容"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#D32F2F">with</span><span style="color:#24292EFF"> col3</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">write</span><span style="color:#212121">(</span><span style="color:#22863A">"第三列内容"</span><span style="color:#212121">)</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 768px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/1a824fab-4cac-4539-a0b2-f09f8db0e870.png" alt="图片"></div><!----></div><ul><li><p data-v-550653ec="" class="paragraph">页面布局-标签栏</p></li></ul><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#24292EFF">tab1</span><span style="color:#212121">,</span><span style="color:#24292EFF"> tab2</span><span style="color:#212121">,</span><span style="color:#24292EFF"> tab3 </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">tabs</span><span style="color:#212121">([</span><span style="color:#22863A">"数据"</span><span style="color:#212121">, </span><span style="color:#22863A">"图表"</span><span style="color:#212121">, </span><span style="color:#22863A">"设置"</span><span style="color:#212121">])</span></span>
<span class="line"></span>
<span class="line"><span style="color:#D32F2F">with</span><span style="color:#24292EFF"> tab1</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">write</span><span style="color:#212121">(</span><span style="color:#22863A">"数据内容"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#D32F2F">with</span><span style="color:#24292EFF"> tab2</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">write</span><span style="color:#212121">(</span><span style="color:#22863A">"图表内容"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#D32F2F">with</span><span style="color:#24292EFF"> tab3</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">write</span><span style="color:#212121">(</span><span style="color:#22863A">"设置内容"</span><span style="color:#212121">)</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 848px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/b3f8f30f-4f6f-477d-b5e6-187a211719a5.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.6.3 实现流程</strong></h4><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 1672px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/85efba76-1f11-4618-8357-363bda921dc0.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph">构建一个完整的AI对话应用需要以下几个关键步骤：</p><p data-v-550653ec="" class="paragraph"><strong>第一步：页面配置和基础结构</strong></p><p data-v-550653ec="" class="paragraph">创建主应用文件 <code>huanhuan_web.py</code>，首先设置页面配置：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> os</span></span>
<span class="line"><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> sys</span></span>
<span class="line"><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> json</span></span>
<span class="line"><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> requests</span></span>
<span class="line"><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> streamlit </span><span style="color:#D32F2F">as</span><span style="color:#24292EFF"> st</span></span>
<span class="line"><span style="color:#D32F2F">from</span><span style="color:#24292EFF"> pathlib </span><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> Path</span></span>
<span class="line"><span style="color:#D32F2F">from</span><span style="color:#24292EFF"> typing </span><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> List</span><span style="color:#212121">,</span><span style="color:#24292EFF"> Dict</span></span>
<span class="line"><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> time</span></span>
<span class="line"><span style="color:#D32F2F">from</span><span style="color:#24292EFF"> datetime </span><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> datetime</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 添加项目根目录到路径</span></span>
<span class="line"><span style="color:#24292EFF">project_root </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> Path</span><span style="color:#212121">(</span><span style="color:#1976D2">__file__</span><span style="color:#212121">).</span><span style="color:#24292EFF">parent</span><span style="color:#212121">.</span><span style="color:#24292EFF">parent</span></span>
<span class="line"><span style="color:#24292EFF">sys</span><span style="color:#212121">.</span><span style="color:#24292EFF">path</span><span style="color:#212121">.</span><span style="color:#6F42C1">insert</span><span style="color:#212121">(</span><span style="color:#1976D2">0</span><span style="color:#212121">, </span><span style="color:#1976D2">str</span><span style="color:#212121">(project_root))</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 页面配置 - 必须在其他Streamlit命令之前</span></span>
<span class="line"><span style="color:#24292EFF">st</span><span style="color:#212121">.</span><span style="color:#6F42C1">set_page_config</span><span style="color:#212121">(</span></span>
<span class="line"><span style="color:#212121">    page_title</span><span style="color:#D32F2F">=</span><span style="color:#22863A">"Chat-嬛嬛 - 甄嬛传角色对话"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">    page_icon</span><span style="color:#D32F2F">=</span><span style="color:#22863A">"👸"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">    layout</span><span style="color:#D32F2F">=</span><span style="color:#22863A">"wide"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">    initial_sidebar_state</span><span style="color:#D32F2F">=</span><span style="color:#22863A">"expanded"</span></span>
<span class="line"><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 自定义CSS样式</span></span>
<span class="line"><span style="color:#24292EFF">st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(</span><span style="color:#22863A">"""</span></span>
<span class="line"><span style="color:#22863A">&lt;style&gt;</span></span>
<span class="line"><span style="color:#22863A">.main-header {</span></span>
<span class="line"><span style="color:#22863A">    text-align: center;</span></span>
<span class="line"><span style="color:#22863A">    color: #8B4513;</span></span>
<span class="line"><span style="color:#22863A">    font-size: 2.5rem;</span></span>
<span class="line"><span style="color:#22863A">    margin-bottom: 1rem;</span></span>
<span class="line"><span style="color:#22863A">}</span></span>
<span class="line"><span style="color:#22863A">.chat-container {</span></span>
<span class="line"><span style="color:#22863A">    max-height: 600px;</span></span>
<span class="line"><span style="color:#22863A">    overflow-y: auto;</span></span>
<span class="line"><span style="color:#22863A">}</span></span>
<span class="line"><span style="color:#22863A">&lt;/style&gt;</span></span>
<span class="line"><span style="color:#22863A">"""</span><span style="color:#212121">, unsafe_allow_html</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">)</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>第二步：应用类设计</strong></p><p data-v-550653ec="" class="paragraph">设计一个主应用类来管理所有功能：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">class</span><span style="color:#6F42C1"> HuanHuanWebApp</span><span style="color:#24292EFF">:</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#C2C3C5">    甄嬛Web应用主类</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#D32F2F">    def</span><span style="color:#6F42C1"> __init__</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#24292EFF">        self</span><span style="color:#212121">.</span><span style="color:#24292EFF">ollama_url </span><span style="color:#D32F2F">=</span><span style="color:#22863A"> "http://localhost:11434"</span></span>
<span class="line"><span style="color:#24292EFF">        self</span><span style="color:#212121">.</span><span style="color:#24292EFF">model_name </span><span style="color:#D32F2F">=</span><span style="color:#22863A"> "huanhuan-qwen"</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#C2C3C5">        # 初始化session state</span></span>
<span class="line"><span style="color:#24292EFF">        self</span><span style="color:#212121">.</span><span style="color:#6F42C1">init_session_state</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    def</span><span style="color:#6F42C1"> init_session_state</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#C2C3C5">        """初始化会话状态"""</span></span>
<span class="line"><span style="color:#C2C3C5">        # 对话历史</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#22863A"> "messages"</span><span style="color:#D32F2F"> not</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">messages </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> []</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#C2C3C5">        # Ollama连接状态</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#22863A"> "ollama_connected"</span><span style="color:#D32F2F"> not</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">ollama_connected </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> False</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#C2C3C5">        # 可用模型列表</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#22863A"> "available_models"</span><span style="color:#D32F2F"> not</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">available_models </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> []</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#C2C3C5">        # 当前选择的模型</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#22863A"> "selected_model"</span><span style="color:#D32F2F"> not</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">selected_model </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> None</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#C2C3C5">        # 生成参数</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#22863A"> "temperature"</span><span style="color:#D32F2F"> not</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">temperature </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> 0.7</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#22863A"> "top_p"</span><span style="color:#D32F2F"> not</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">top_p </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> 0.9</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#22863A"> "top_k"</span><span style="color:#D32F2F"> not</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">top_k </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> 40</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#22863A"> "max_tokens"</span><span style="color:#D32F2F"> not</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">max_tokens </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> 256</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#C2C3C5">        # 对话历史记录</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#22863A"> 'chat_history'</span><span style="color:#D32F2F"> not</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">chat_history </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> []</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>第三步：Ollama服务连接管理</strong></p><p data-v-550653ec="" class="paragraph">实现与Ollama服务的连接检查和模型获取：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> check_ollama_connection</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">) </span><span style="color:#212121">-&gt;</span><span style="color:#1976D2"> bool</span><span style="color:#24292EFF">:</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#C2C3C5">    检查Ollama服务连接状态</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#D32F2F">    try</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">        response </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> requests</span><span style="color:#212121">.</span><span style="color:#6F42C1">get</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"</span><span style="color:#1976D2">{</span><span style="color:#212121">self.ollama_url</span><span style="color:#1976D2">}</span><span style="color:#22863A">/api/tags"</span><span style="color:#212121">, timeout</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">5</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#24292EFF"> response</span><span style="color:#212121">.</span><span style="color:#24292EFF">status_code </span><span style="color:#D32F2F">==</span><span style="color:#1976D2"> 200</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">ollama_connected </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> True</span></span>
<span class="line"><span style="color:#D32F2F">            return</span><span style="color:#1976D2"> True</span></span>
<span class="line"><span style="color:#D32F2F">    except</span><span style="color:#24292EFF"> requests</span><span style="color:#212121">.</span><span style="color:#24292EFF">exceptions</span><span style="color:#212121">.</span><span style="color:#24292EFF">RequestException</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">        pass</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">ollama_connected </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> False</span></span>
<span class="line"><span style="color:#D32F2F">    return</span><span style="color:#1976D2"> False</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> get_available_models</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">) </span><span style="color:#212121">-&gt;</span><span style="color:#24292EFF"> List</span><span style="color:#212121">[</span><span style="color:#1976D2">str</span><span style="color:#212121">]</span><span style="color:#24292EFF">:</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#C2C3C5">    获取可用的模型列表</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#D32F2F">    if</span><span style="color:#D32F2F"> not</span><span style="color:#24292EFF"> self</span><span style="color:#212121">.</span><span style="color:#6F42C1">check_ollama_connection</span><span style="color:#212121">():</span></span>
<span class="line"><span style="color:#D32F2F">        return</span><span style="color:#24292EFF"> []</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    try</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">        response </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> requests</span><span style="color:#212121">.</span><span style="color:#6F42C1">get</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"</span><span style="color:#1976D2">{</span><span style="color:#212121">self.ollama_url</span><span style="color:#1976D2">}</span><span style="color:#22863A">/api/tags"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#24292EFF"> response</span><span style="color:#212121">.</span><span style="color:#24292EFF">status_code </span><span style="color:#D32F2F">==</span><span style="color:#1976D2"> 200</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            data </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> response</span><span style="color:#212121">.</span><span style="color:#6F42C1">json</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">            models </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> [model</span><span style="color:#212121">[</span><span style="color:#22863A">'name'</span><span style="color:#212121">]</span><span style="color:#D32F2F"> for</span><span style="color:#24292EFF"> model </span><span style="color:#D32F2F">in</span><span style="color:#24292EFF"> data</span><span style="color:#212121">.</span><span style="color:#6F42C1">get</span><span style="color:#212121">(</span><span style="color:#22863A">'models'</span><span style="color:#212121">, [])</span><span style="color:#24292EFF">]</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">available_models </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> models</span></span>
<span class="line"><span style="color:#D32F2F">            return</span><span style="color:#24292EFF"> models</span></span>
<span class="line"><span style="color:#D32F2F">    except</span><span style="color:#1976D2"> Exception</span><span style="color:#D32F2F"> as</span><span style="color:#24292EFF"> e</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">error</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"获取模型列表失败: </span><span style="color:#1976D2">{</span><span style="color:#212121">e</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    return</span><span style="color:#24292EFF"> []</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>第四步：流式对话实现</strong></p><p data-v-550653ec="" class="paragraph">实现与AI模型的流式对话功能：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> stream_chat</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#212121">,</span><span style="color:#FF9800"> messages</span><span style="color:#212121">,</span><span style="color:#FF9800"> model</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#C2C3C5">    流式对话生成</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#24292EFF">    url </span><span style="color:#D32F2F">=</span><span style="color:#D32F2F"> f</span><span style="color:#22863A">"</span><span style="color:#1976D2">{</span><span style="color:#24292EFF">self</span><span style="color:#212121">.</span><span style="color:#24292EFF">ollama_host</span><span style="color:#1976D2">}</span><span style="color:#22863A">/api/chat"</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#24292EFF">    payload </span><span style="color:#D32F2F">=</span><span style="color:#212121"> {</span></span>
<span class="line"><span style="color:#22863A">        "model"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> model</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">        "messages"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> messages</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">        "stream"</span><span style="color:#212121">:</span><span style="color:#1976D2"> True</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">        "options"</span><span style="color:#212121">:</span><span style="color:#212121"> {</span></span>
<span class="line"><span style="color:#22863A">            "temperature"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">temperature</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">            "top_p"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">top_p</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">            "top_k"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">top_k</span></span>
<span class="line"><span style="color:#212121">        }</span></span>
<span class="line"><span style="color:#212121">    }</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    try</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">        response </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> requests</span><span style="color:#212121">.</span><span style="color:#6F42C1">post</span><span style="color:#212121">(url, json</span><span style="color:#D32F2F">=</span><span style="color:#212121">payload, stream</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">, timeout</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">60</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        response</span><span style="color:#212121">.</span><span style="color:#6F42C1">raise_for_status</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#D32F2F">        for</span><span style="color:#24292EFF"> line </span><span style="color:#D32F2F">in</span><span style="color:#24292EFF"> response</span><span style="color:#212121">.</span><span style="color:#6F42C1">iter_lines</span><span style="color:#212121">():</span></span>
<span class="line"><span style="color:#D32F2F">            if</span><span style="color:#24292EFF"> line</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">                try</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                    data </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> json</span><span style="color:#212121">.</span><span style="color:#6F42C1">loads</span><span style="color:#212121">(line.</span><span style="color:#6F42C1">decode</span><span style="color:#212121">(</span><span style="color:#22863A">'utf-8'</span><span style="color:#212121">))</span></span>
<span class="line"><span style="color:#D32F2F">                    if</span><span style="color:#22863A"> 'message'</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> data </span><span style="color:#D32F2F">and</span><span style="color:#22863A"> 'content'</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> data</span><span style="color:#212121">[</span><span style="color:#22863A">'message'</span><span style="color:#212121">]:</span></span>
<span class="line"><span style="color:#D32F2F">                        yield</span><span style="color:#24292EFF"> data</span><span style="color:#212121">[</span><span style="color:#22863A">'message'</span><span style="color:#212121">]</span><span style="color:#24292EFF">[</span><span style="color:#22863A">'content'</span><span style="color:#24292EFF">]</span></span>
<span class="line"><span style="color:#24292EFF">                    </span></span>
<span class="line"><span style="color:#D32F2F">                    if</span><span style="color:#24292EFF"> data</span><span style="color:#212121">.</span><span style="color:#6F42C1">get</span><span style="color:#212121">(</span><span style="color:#22863A">'done'</span><span style="color:#212121">, </span><span style="color:#1976D2">False</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#D32F2F">                        break</span></span>
<span class="line"><span style="color:#D32F2F">                except</span><span style="color:#24292EFF"> json</span><span style="color:#212121">.</span><span style="color:#24292EFF">JSONDecodeError</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">                    continue</span></span>
<span class="line"><span style="color:#24292EFF">                    </span></span>
<span class="line"><span style="color:#D32F2F">    except</span><span style="color:#24292EFF"> requests</span><span style="color:#212121">.</span><span style="color:#24292EFF">exceptions</span><span style="color:#212121">.</span><span style="color:#24292EFF">RequestException </span><span style="color:#D32F2F">as</span><span style="color:#24292EFF"> e</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">        yield</span><span style="color:#D32F2F"> f</span><span style="color:#22863A">"连接错误: </span><span style="color:#1976D2">{</span><span style="color:#24292EFF">e</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span></span>
<span class="line"><span style="color:#D32F2F">    except</span><span style="color:#1976D2"> Exception</span><span style="color:#D32F2F"> as</span><span style="color:#24292EFF"> e</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">        yield</span><span style="color:#D32F2F"> f</span><span style="color:#22863A">"未知错误: </span><span style="color:#1976D2">{</span><span style="color:#24292EFF">e</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>第五步：用户界面构建</strong></p><p data-v-550653ec="" class="paragraph">构建完整的用户界面，包括侧边栏和主内容区：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> render_sidebar</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#C2C3C5">    渲染侧边栏</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#D32F2F">    with</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">sidebar</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">title</span><span style="color:#212121">(</span><span style="color:#22863A">"⚙️ 设置"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#C2C3C5">        # 连接状态</span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#24292EFF"> self</span><span style="color:#212121">.</span><span style="color:#6F42C1">check_ollama_connection</span><span style="color:#212121">():</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#6F42C1">success</span><span style="color:#212121">(</span><span style="color:#22863A">"🟢 Ollama服务已连接"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">        else</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#6F42C1">error</span><span style="color:#212121">(</span><span style="color:#22863A">"🔴 Ollama服务未连接"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#6F42C1">info</span><span style="color:#212121">(</span><span style="color:#22863A">"请确保Ollama服务正在运行"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">divider</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#C2C3C5">        # 模型选择</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">subheader</span><span style="color:#212121">(</span><span style="color:#22863A">"🤖 模型设置"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        available_models </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> self</span><span style="color:#212121">.</span><span style="color:#6F42C1">get_available_models</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#D32F2F">        if</span><span style="color:#24292EFF"> available_models</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">            if</span><span style="color:#24292EFF"> self</span><span style="color:#212121">.</span><span style="color:#24292EFF">model_name </span><span style="color:#D32F2F">in</span><span style="color:#24292EFF"> available_models</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                default_index </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> available_models</span><span style="color:#212121">.</span><span style="color:#6F42C1">index</span><span style="color:#212121">(self.model_name)</span></span>
<span class="line"><span style="color:#D32F2F">            else</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                default_index </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> 0</span></span>
<span class="line"><span style="color:#24292EFF">            </span></span>
<span class="line"><span style="color:#24292EFF">            selected_model </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">selectbox</span><span style="color:#212121">(</span></span>
<span class="line"><span style="color:#22863A">                "选择模型"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">                available_models,</span></span>
<span class="line"><span style="color:#212121">                index</span><span style="color:#D32F2F">=</span><span style="color:#212121">default_index</span></span>
<span class="line"><span style="color:#212121">            )</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">selected_model </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> selected_model</span></span>
<span class="line"><span style="color:#24292EFF">            self</span><span style="color:#212121">.</span><span style="color:#24292EFF">model_name </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> selected_model</span></span>
<span class="line"><span style="color:#D32F2F">        else</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#6F42C1">warning</span><span style="color:#212121">(</span><span style="color:#22863A">"未找到可用模型"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#6F42C1">info</span><span style="color:#212121">(</span><span style="color:#22863A">"请先部署甄嬛模型"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">divider</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#C2C3C5">        # 参数调节</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">subheader</span><span style="color:#212121">(</span><span style="color:#22863A">"🎛️ 生成参数"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">temperature </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">slider</span><span style="color:#212121">(</span></span>
<span class="line"><span style="color:#22863A">            "Temperature (创造性)"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            min_value</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">0.1</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            max_value</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">2.0</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            value</span><span style="color:#D32F2F">=</span><span style="color:#212121">st.session_state.temperature,</span></span>
<span class="line"><span style="color:#212121">            step</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">0.1</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            help</span><span style="color:#D32F2F">=</span><span style="color:#22863A">"控制回答的随机性，值越高越有创造性"</span></span>
<span class="line"><span style="color:#212121">        )</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">top_p </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">slider</span><span style="color:#212121">(</span></span>
<span class="line"><span style="color:#22863A">            "Top P (多样性)"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            min_value</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">0.1</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            max_value</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">1.0</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            value</span><span style="color:#D32F2F">=</span><span style="color:#212121">st.session_state.top_p,</span></span>
<span class="line"><span style="color:#212121">            step</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">0.1</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            help</span><span style="color:#D32F2F">=</span><span style="color:#22863A">"控制词汇选择的多样性"</span></span>
<span class="line"><span style="color:#212121">        )</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">top_k </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">slider</span><span style="color:#212121">(</span></span>
<span class="line"><span style="color:#22863A">            "Top K (词汇范围)"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            min_value</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">1</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            max_value</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">100</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            value</span><span style="color:#D32F2F">=</span><span style="color:#212121">st.session_state.top_k,</span></span>
<span class="line"><span style="color:#212121">            step</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">1</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            help</span><span style="color:#D32F2F">=</span><span style="color:#22863A">"限制每步选择的词汇数量"</span></span>
<span class="line"><span style="color:#212121">        )</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">max_tokens </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">slider</span><span style="color:#212121">(</span></span>
<span class="line"><span style="color:#22863A">            "Max Tokens (回答长度)"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            min_value</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">50</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            max_value</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">500</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            value</span><span style="color:#D32F2F">=</span><span style="color:#212121">st.session_state.max_tokens,</span></span>
<span class="line"><span style="color:#212121">            step</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">10</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">            help</span><span style="color:#D32F2F">=</span><span style="color:#22863A">"控制回答的最大长度"</span></span>
<span class="line"><span style="color:#212121">        )</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">divider</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#C2C3C5">        # 功能按钮</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">subheader</span><span style="color:#212121">(</span><span style="color:#22863A">"🛠️ 功能"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#24292EFF">        col1</span><span style="color:#212121">,</span><span style="color:#24292EFF"> col2</span><span style="color:#212121">,</span><span style="color:#24292EFF"> col3 </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">columns</span><span style="color:#212121">(</span><span style="color:#1976D2">3</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#D32F2F">        with</span><span style="color:#24292EFF"> col1</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">            if</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">button</span><span style="color:#212121">(</span><span style="color:#22863A">"🗑️ 清空对话"</span><span style="color:#212121">, use_container_width</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#24292EFF">                st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">messages </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> []</span></span>
<span class="line"><span style="color:#24292EFF">                st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">chat_history </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> []</span></span>
<span class="line"><span style="color:#24292EFF">                st</span><span style="color:#212121">.</span><span style="color:#6F42C1">rerun</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#D32F2F">        with</span><span style="color:#24292EFF"> col2</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">            if</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">button</span><span style="color:#212121">(</span><span style="color:#22863A">"💾 保存对话"</span><span style="color:#212121">, use_container_width</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#D32F2F">                if</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">chat_history</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                    self</span><span style="color:#212121">.</span><span style="color:#6F42C1">save_chat_history</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">                    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">success</span><span style="color:#212121">(</span><span style="color:#22863A">"对话已保存！"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">                else</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">warning</span><span style="color:#212121">(</span><span style="color:#22863A">"没有对话内容可保存"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#D32F2F">        with</span><span style="color:#24292EFF"> col3</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">            if</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">button</span><span style="color:#212121">(</span><span style="color:#22863A">"📂 加载对话"</span><span style="color:#212121">, use_container_width</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#24292EFF">                self</span><span style="color:#212121">.</span><span style="color:#6F42C1">load_chat_history</span><span style="color:#212121">()</span></span>
<span class="line"></span>
<span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> render_main_content</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#C2C3C5">    渲染主要内容</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#C2C3C5">    # 标题和介绍</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">title</span><span style="color:#212121">(</span><span style="color:#22863A">"👸 Chat-嬛嬛"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(</span><span style="color:#22863A">"""</span></span>
<span class="line"><span style="color:#22863A">    欢迎来到甄嬛传角色对话系统！我是甄嬛，大理寺少卿甄远道之女。</span></span>
<span class="line"><span style="color:#22863A">    臣妾愿与您畅谈宫廷生活、诗词歌赋，分享人生感悟。</span></span>
<span class="line"><span style="color:#22863A">    """</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 角色信息卡片</span></span>
<span class="line"><span style="color:#D32F2F">    with</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">expander</span><span style="color:#212121">(</span><span style="color:#22863A">"📖 角色信息"</span><span style="color:#212121">, expanded</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">False</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#24292EFF">        col1</span><span style="color:#212121">,</span><span style="color:#24292EFF"> col2 </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">columns</span><span style="color:#212121">(</span><span style="color:#1976D2">2</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#D32F2F">        with</span><span style="color:#24292EFF"> col1</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(</span><span style="color:#22863A">"""</span></span>
<span class="line"><span style="color:#22863A">            **基本信息**</span></span>
<span class="line"><span style="color:#22863A">            - 姓名：甄嬛（甄玉嬛）</span></span>
<span class="line"><span style="color:#22863A">            - 身份：熹贵妃</span></span>
<span class="line"><span style="color:#22863A">            - 出身：大理寺少卿甄远道之女</span></span>
<span class="line"><span style="color:#22863A">            - 特长：诗词歌赋、琴棋书画</span></span>
<span class="line"><span style="color:#22863A">            """</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#D32F2F">        with</span><span style="color:#24292EFF"> col2</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(</span><span style="color:#22863A">"""</span></span>
<span class="line"><span style="color:#22863A">            **性格特点**</span></span>
<span class="line"><span style="color:#22863A">            - 聪慧机智，善于应变</span></span>
<span class="line"><span style="color:#22863A">            - 温婉贤淑，知书达理</span></span>
<span class="line"><span style="color:#22863A">            - 坚韧不拔，重情重义</span></span>
<span class="line"><span style="color:#22863A">            - 语言典雅，谦逊有礼</span></span>
<span class="line"><span style="color:#22863A">            """</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 示例问题</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">subheader</span><span style="color:#212121">(</span><span style="color:#22863A">"💡 示例问题"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    example_questions </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> [</span></span>
<span class="line"><span style="color:#22863A">        "你好，请介绍一下自己"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">        "你觉得宫廷生活如何？"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">        "如何看待友情？"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">        "能为我作一首诗吗？"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">        "给后人一些人生建议"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">        "你最喜欢什么？"</span></span>
<span class="line"><span style="color:#24292EFF">    ]</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#24292EFF">    cols </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">columns</span><span style="color:#212121">(</span><span style="color:#1976D2">3</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">    for</span><span style="color:#24292EFF"> i</span><span style="color:#212121">,</span><span style="color:#24292EFF"> question </span><span style="color:#D32F2F">in</span><span style="color:#6F42C1"> enumerate</span><span style="color:#212121">(example_questions):</span></span>
<span class="line"><span style="color:#D32F2F">        with</span><span style="color:#24292EFF"> cols</span><span style="color:#212121">[</span><span style="color:#24292EFF">i </span><span style="color:#D32F2F">%</span><span style="color:#1976D2"> 3</span><span style="color:#212121">]:</span></span>
<span class="line"><span style="color:#D32F2F">            if</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">button</span><span style="color:#212121">(question, key</span><span style="color:#D32F2F">=</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"example_</span><span style="color:#1976D2">{</span><span style="color:#212121">i</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">, use_container_width</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#24292EFF">                st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">current_question </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> question</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">divider</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 对话历史</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">subheader</span><span style="color:#212121">(</span><span style="color:#22863A">"💬 对话历史"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 显示对话消息</span></span>
<span class="line"><span style="color:#D32F2F">    for</span><span style="color:#24292EFF"> message </span><span style="color:#D32F2F">in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">messages</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">        with</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">chat_message</span><span style="color:#212121">(message[</span><span style="color:#22863A">"role"</span><span style="color:#212121">]):</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(message[</span><span style="color:#22863A">"content"</span><span style="color:#212121">])</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 处理示例问题</span></span>
<span class="line"><span style="color:#D32F2F">    if</span><span style="color:#6F42C1"> hasattr</span><span style="color:#212121">(st.session_state, </span><span style="color:#22863A">'current_question'</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#24292EFF">        user_input </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">current_question</span></span>
<span class="line"><span style="color:#6F42C1">        delattr</span><span style="color:#212121">(st.session_state, </span><span style="color:#22863A">'current_question'</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">    else</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">        user_input </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> None</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 聊天输入</span></span>
<span class="line"><span style="color:#D32F2F">    if</span><span style="color:#24292EFF"> prompt </span><span style="color:#D32F2F">:=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">chat_input</span><span style="color:#212121">(</span><span style="color:#22863A">"请输入您的问题..."</span><span style="color:#212121">)</span><span style="color:#D32F2F"> or</span><span style="color:#24292EFF"> user_input</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#C2C3C5">        # 添加用户消息</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">messages</span><span style="color:#212121">.</span><span style="color:#6F42C1">append</span><span style="color:#212121">({</span><span style="color:#22863A">"role"</span><span style="color:#212121">: </span><span style="color:#22863A">"user"</span><span style="color:#212121">, </span><span style="color:#22863A">"content"</span><span style="color:#212121">: prompt})</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#D32F2F">        with</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">chat_message</span><span style="color:#212121">(</span><span style="color:#22863A">"user"</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(prompt)</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#C2C3C5">        # 生成回复</span></span>
<span class="line"><span style="color:#D32F2F">        with</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">chat_message</span><span style="color:#212121">(</span><span style="color:#22863A">"assistant"</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#D32F2F">            with</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">spinner</span><span style="color:#212121">(</span><span style="color:#22863A">"甄嬛正在思考..."</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#C2C3C5">                # 使用流式生成</span></span>
<span class="line"><span style="color:#24292EFF">                response_placeholder </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">empty</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">                full_response </span><span style="color:#D32F2F">=</span><span style="color:#22863A"> ""</span></span>
<span class="line"><span style="color:#24292EFF">                </span></span>
<span class="line"><span style="color:#C2C3C5">                # 构建消息历史</span></span>
<span class="line"><span style="color:#24292EFF">                messages </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> []</span></span>
<span class="line"><span style="color:#D32F2F">                for</span><span style="color:#24292EFF"> msg </span><span style="color:#D32F2F">in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">messages</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                    messages</span><span style="color:#212121">.</span><span style="color:#6F42C1">append</span><span style="color:#212121">({</span></span>
<span class="line"><span style="color:#22863A">                        "role"</span><span style="color:#212121">: msg[</span><span style="color:#22863A">"role"</span><span style="color:#212121">],</span></span>
<span class="line"><span style="color:#22863A">                        "content"</span><span style="color:#212121">: msg[</span><span style="color:#22863A">"content"</span><span style="color:#212121">]</span></span>
<span class="line"><span style="color:#212121">                    })</span></span>
<span class="line"><span style="color:#24292EFF">                </span></span>
<span class="line"><span style="color:#D32F2F">                for</span><span style="color:#24292EFF"> chunk </span><span style="color:#D32F2F">in</span><span style="color:#24292EFF"> self</span><span style="color:#212121">.</span><span style="color:#6F42C1">stream_chat</span><span style="color:#212121">(messages, st.session_state.selected_model):</span></span>
<span class="line"><span style="color:#24292EFF">                    full_response </span><span style="color:#D32F2F">+=</span><span style="color:#24292EFF"> chunk</span></span>
<span class="line"><span style="color:#24292EFF">                    response_placeholder</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(full_response </span><span style="color:#D32F2F">+</span><span style="color:#22863A"> "▌"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">                </span></span>
<span class="line"><span style="color:#24292EFF">                response_placeholder</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(full_response)</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#C2C3C5">        # 添加助手消息</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">messages</span><span style="color:#212121">.</span><span style="color:#6F42C1">append</span><span style="color:#212121">({</span><span style="color:#22863A">"role"</span><span style="color:#212121">: </span><span style="color:#22863A">"assistant"</span><span style="color:#212121">, </span><span style="color:#22863A">"content"</span><span style="color:#212121">: full_response})</span></span>
<span class="line"><span style="color:#24292EFF">        </span></span>
<span class="line"><span style="color:#C2C3C5">        # 保存到历史记录</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">chat_history</span><span style="color:#212121">.</span><span style="color:#6F42C1">append</span><span style="color:#212121">({</span></span>
<span class="line"><span style="color:#22863A">            "timestamp"</span><span style="color:#212121">: datetime.</span><span style="color:#6F42C1">now</span><span style="color:#212121">().</span><span style="color:#6F42C1">isoformat</span><span style="color:#212121">(),</span></span>
<span class="line"><span style="color:#22863A">            "user"</span><span style="color:#212121">: prompt,</span></span>
<span class="line"><span style="color:#22863A">            "assistant"</span><span style="color:#212121">: full_response,</span></span>
<span class="line"><span style="color:#22863A">            "params"</span><span style="color:#212121">: {</span></span>
<span class="line"><span style="color:#22863A">                "temperature"</span><span style="color:#212121">: st.session_state.temperature,</span></span>
<span class="line"><span style="color:#22863A">                "top_p"</span><span style="color:#212121">: st.session_state.top_p,</span></span>
<span class="line"><span style="color:#22863A">                "top_k"</span><span style="color:#212121">: st.session_state.top_k,</span></span>
<span class="line"><span style="color:#22863A">                "max_tokens"</span><span style="color:#212121">: st.session_state.max_tokens</span></span>
<span class="line"><span style="color:#212121">            }</span></span>
<span class="line"><span style="color:#212121">        })</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>第六步：对话历史管理</strong></p><p data-v-550653ec="" class="paragraph">添加保存和加载对话历史的功能：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> save_chat_history</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#C2C3C5">    保存对话历史</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#D32F2F">    if</span><span style="color:#D32F2F"> not</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">chat_history</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">        return</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 创建保存目录</span></span>
<span class="line"><span style="color:#24292EFF">    save_dir </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> Path</span><span style="color:#212121">(</span><span style="color:#22863A">"application/chat_history"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    save_dir</span><span style="color:#212121">.</span><span style="color:#6F42C1">mkdir</span><span style="color:#212121">(parents</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">, exist_ok</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 生成文件名</span></span>
<span class="line"><span style="color:#24292EFF">    timestamp </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> datetime</span><span style="color:#212121">.</span><span style="color:#6F42C1">now</span><span style="color:#212121">().</span><span style="color:#6F42C1">strftime</span><span style="color:#212121">(</span><span style="color:#22863A">"%Y%m</span><span style="color:#1976D2">%d</span><span style="color:#22863A">_%H%M%S"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    filename </span><span style="color:#D32F2F">=</span><span style="color:#D32F2F"> f</span><span style="color:#22863A">"huanhuan_chat_</span><span style="color:#1976D2">{</span><span style="color:#24292EFF">timestamp</span><span style="color:#1976D2">}</span><span style="color:#22863A">.json"</span></span>
<span class="line"><span style="color:#24292EFF">    filepath </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> save_dir </span><span style="color:#D32F2F">/</span><span style="color:#24292EFF"> filename</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 保存数据</span></span>
<span class="line"><span style="color:#24292EFF">    save_data </span><span style="color:#D32F2F">=</span><span style="color:#212121"> {</span></span>
<span class="line"><span style="color:#22863A">        "timestamp"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> timestamp</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">        "chat_history"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">chat_history</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">        "model_params"</span><span style="color:#212121">:</span><span style="color:#212121"> {</span></span>
<span class="line"><span style="color:#22863A">            "selected_model"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">selected_model</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">            "temperature"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">temperature</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">            "top_p"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">top_p</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">            "top_k"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">top_k</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#22863A">            "max_tokens"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">max_tokens</span></span>
<span class="line"><span style="color:#212121">        }</span></span>
<span class="line"><span style="color:#212121">    }</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    try</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">        with</span><span style="color:#6F42C1"> open</span><span style="color:#212121">(filepath, </span><span style="color:#22863A">'w'</span><span style="color:#212121">, encoding</span><span style="color:#D32F2F">=</span><span style="color:#22863A">'utf-8'</span><span style="color:#212121">)</span><span style="color:#D32F2F"> as</span><span style="color:#24292EFF"> f</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            json</span><span style="color:#212121">.</span><span style="color:#6F42C1">dump</span><span style="color:#212121">(save_data, f, ensure_ascii</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">False</span><span style="color:#212121">, indent</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">2</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">    except</span><span style="color:#1976D2"> Exception</span><span style="color:#D32F2F"> as</span><span style="color:#24292EFF"> e</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">error</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"保存失败: </span><span style="color:#1976D2">{</span><span style="color:#212121">e</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> load_chat_history</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#C2C3C5">    加载对话历史</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#24292EFF">    save_dir </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> Path</span><span style="color:#212121">(</span><span style="color:#22863A">"application/chat_history"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">    if</span><span style="color:#D32F2F"> not</span><span style="color:#24292EFF"> save_dir</span><span style="color:#212121">.</span><span style="color:#6F42C1">exists</span><span style="color:#212121">():</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">warning</span><span style="color:#212121">(</span><span style="color:#22863A">"没有找到历史对话文件"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">        return</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 获取所有历史文件</span></span>
<span class="line"><span style="color:#24292EFF">    history_files </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> list</span><span style="color:#212121">(save_dir.</span><span style="color:#6F42C1">glob</span><span style="color:#212121">(</span><span style="color:#22863A">"*.json"</span><span style="color:#212121">))</span></span>
<span class="line"><span style="color:#D32F2F">    if</span><span style="color:#D32F2F"> not</span><span style="color:#24292EFF"> history_files</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">warning</span><span style="color:#212121">(</span><span style="color:#22863A">"没有找到历史对话文件"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">        return</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#C2C3C5">    # 选择文件</span></span>
<span class="line"><span style="color:#24292EFF">    file_options </span><span style="color:#D32F2F">=</span><span style="color:#212121"> {</span><span style="color:#24292EFF">f</span><span style="color:#212121">.</span><span style="color:#24292EFF">name</span><span style="color:#212121">:</span><span style="color:#24292EFF"> f </span><span style="color:#D32F2F">for</span><span style="color:#24292EFF"> f </span><span style="color:#D32F2F">in</span><span style="color:#6F42C1"> sorted</span><span style="color:#212121">(history_files, reverse</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">)}</span></span>
<span class="line"><span style="color:#24292EFF">    selected_file </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">selectbox</span><span style="color:#212121">(</span></span>
<span class="line"><span style="color:#22863A">        "选择要加载的对话:"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#212121">        options</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">list</span><span style="color:#212121">(file_options.</span><span style="color:#6F42C1">keys</span><span style="color:#212121">())</span></span>
<span class="line"><span style="color:#212121">    )</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    if</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">button</span><span style="color:#212121">(</span><span style="color:#22863A">"加载选中的对话"</span><span style="color:#212121">):</span></span>
<span class="line"><span style="color:#D32F2F">        try</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#D32F2F">            with</span><span style="color:#6F42C1"> open</span><span style="color:#212121">(file_options[selected_file], </span><span style="color:#22863A">'r'</span><span style="color:#212121">, encoding</span><span style="color:#D32F2F">=</span><span style="color:#22863A">'utf-8'</span><span style="color:#212121">)</span><span style="color:#D32F2F"> as</span><span style="color:#24292EFF"> f</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                data </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> json</span><span style="color:#212121">.</span><span style="color:#6F42C1">load</span><span style="color:#212121">(f)</span></span>
<span class="line"><span style="color:#24292EFF">            </span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">chat_history </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> data</span><span style="color:#212121">.</span><span style="color:#6F42C1">get</span><span style="color:#212121">(</span><span style="color:#22863A">'chat_history'</span><span style="color:#212121">, [])</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">messages </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> []</span></span>
<span class="line"><span style="color:#24292EFF">            </span></span>
<span class="line"><span style="color:#C2C3C5">            # 重建messages格式</span></span>
<span class="line"><span style="color:#D32F2F">            for</span><span style="color:#24292EFF"> chat </span><span style="color:#D32F2F">in</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">chat_history</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">messages</span><span style="color:#212121">.</span><span style="color:#6F42C1">append</span><span style="color:#212121">({</span><span style="color:#22863A">"role"</span><span style="color:#212121">: </span><span style="color:#22863A">"user"</span><span style="color:#212121">, </span><span style="color:#22863A">"content"</span><span style="color:#212121">: chat[</span><span style="color:#22863A">"user"</span><span style="color:#212121">]})</span></span>
<span class="line"><span style="color:#24292EFF">                st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">messages</span><span style="color:#212121">.</span><span style="color:#6F42C1">append</span><span style="color:#212121">({</span><span style="color:#22863A">"role"</span><span style="color:#212121">: </span><span style="color:#22863A">"assistant"</span><span style="color:#212121">, </span><span style="color:#22863A">"content"</span><span style="color:#212121">: chat[</span><span style="color:#22863A">"assistant"</span><span style="color:#212121">]})</span></span>
<span class="line"><span style="color:#24292EFF">            </span></span>
<span class="line"><span style="color:#C2C3C5">            # 加载模型参数</span></span>
<span class="line"><span style="color:#D32F2F">            if</span><span style="color:#22863A"> 'model_params'</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> data</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                params </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> data</span><span style="color:#212121">[</span><span style="color:#22863A">'model_params'</span><span style="color:#212121">]</span></span>
<span class="line"><span style="color:#D32F2F">                if</span><span style="color:#22863A"> 'selected_model'</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> params</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                    st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">selected_model </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> params</span><span style="color:#212121">[</span><span style="color:#22863A">'selected_model'</span><span style="color:#212121">]</span></span>
<span class="line"><span style="color:#D32F2F">                if</span><span style="color:#22863A"> 'temperature'</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> params</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                    st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">temperature </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> params</span><span style="color:#212121">[</span><span style="color:#22863A">'temperature'</span><span style="color:#212121">]</span></span>
<span class="line"><span style="color:#D32F2F">                if</span><span style="color:#22863A"> 'top_p'</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> params</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                    st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">top_p </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> params</span><span style="color:#212121">[</span><span style="color:#22863A">'top_p'</span><span style="color:#212121">]</span></span>
<span class="line"><span style="color:#D32F2F">                if</span><span style="color:#22863A"> 'top_k'</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> params</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                    st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">top_k </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> params</span><span style="color:#212121">[</span><span style="color:#22863A">'top_k'</span><span style="color:#212121">]</span></span>
<span class="line"><span style="color:#D32F2F">                if</span><span style="color:#22863A"> 'max_tokens'</span><span style="color:#D32F2F"> in</span><span style="color:#24292EFF"> params</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">                    st</span><span style="color:#212121">.</span><span style="color:#24292EFF">session_state</span><span style="color:#212121">.</span><span style="color:#24292EFF">max_tokens </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> params</span><span style="color:#212121">[</span><span style="color:#22863A">'max_tokens'</span><span style="color:#212121">]</span></span>
<span class="line"><span style="color:#24292EFF">            </span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#6F42C1">success</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"已加载对话: </span><span style="color:#1976D2">{</span><span style="color:#212121">selected_file</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#6F42C1">rerun</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#D32F2F">        except</span><span style="color:#1976D2"> Exception</span><span style="color:#D32F2F"> as</span><span style="color:#24292EFF"> e</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">            st</span><span style="color:#212121">.</span><span style="color:#6F42C1">error</span><span style="color:#212121">(</span><span style="color:#D32F2F">f</span><span style="color:#22863A">"加载失败: </span><span style="color:#1976D2">{</span><span style="color:#212121">e</span><span style="color:#1976D2">}</span><span style="color:#22863A">"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> get_history_files</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#C2C3C5">    获取历史文件列表</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#24292EFF">    save_dir </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> Path</span><span style="color:#212121">(</span><span style="color:#22863A">"application/chat_history"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#D32F2F">    if</span><span style="color:#D32F2F"> not</span><span style="color:#24292EFF"> save_dir</span><span style="color:#212121">.</span><span style="color:#6F42C1">exists</span><span style="color:#212121">():</span></span>
<span class="line"><span style="color:#D32F2F">        return</span><span style="color:#24292EFF"> []</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#24292EFF">    history_files </span><span style="color:#D32F2F">=</span><span style="color:#1976D2"> list</span><span style="color:#212121">(save_dir.</span><span style="color:#6F42C1">glob</span><span style="color:#212121">(</span><span style="color:#22863A">"*.json"</span><span style="color:#212121">))</span></span>
<span class="line"><span style="color:#D32F2F">    return</span><span style="color:#6F42C1"> sorted</span><span style="color:#212121">([f.name </span><span style="color:#D32F2F">for</span><span style="color:#212121"> f </span><span style="color:#D32F2F">in</span><span style="color:#212121"> history_files], reverse</span><span style="color:#D32F2F">=</span><span style="color:#1976D2">True</span><span style="color:#212121">)</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>第七步：页脚渲染</strong></p><p data-v-550653ec="" class="paragraph">添加页脚统计信息：</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> render_footer</span><span style="color:#24292EFF">(</span><span style="color:#FF9800">self</span><span style="color:#24292EFF">):</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#C2C3C5">    渲染页脚</span></span>
<span class="line"><span style="color:#C2C3C5">    """</span></span>
<span class="line"><span style="color:#24292EFF">    st</span><span style="color:#212121">.</span><span style="color:#6F42C1">divider</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#24292EFF">    col1</span><span style="color:#212121">,</span><span style="color:#24292EFF"> col2</span><span style="color:#212121">,</span><span style="color:#24292EFF"> col3 </span><span style="color:#D32F2F">=</span><span style="color:#24292EFF"> st</span><span style="color:#212121">.</span><span style="color:#6F42C1">columns</span><span style="color:#212121">(</span><span style="color:#1976D2">3</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    with</span><span style="color:#24292EFF"> col1</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(</span><span style="color:#22863A">"**📊 统计信息**"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">metric</span><span style="color:#212121">(</span><span style="color:#22863A">"对话轮数"</span><span style="color:#212121">, </span><span style="color:#6F42C1">len</span><span style="color:#212121">(st.session_state.messages) </span><span style="color:#D32F2F">//</span><span style="color:#1976D2"> 2</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    with</span><span style="color:#24292EFF"> col2</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(</span><span style="color:#22863A">"**🔧 技术栈**"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(</span><span style="color:#22863A">"Streamlit + Ollama + LoRA"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">    </span></span>
<span class="line"><span style="color:#D32F2F">    with</span><span style="color:#24292EFF"> col3</span><span style="color:#212121">:</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(</span><span style="color:#22863A">"**📚 参考项目**"</span><span style="color:#212121">)</span></span>
<span class="line"><span style="color:#24292EFF">        st</span><span style="color:#212121">.</span><span style="color:#6F42C1">markdown</span><span style="color:#212121">(</span><span style="color:#22863A">"[huanhuan-chat](https://github.com/KMnO4-zx/huanhuan-chat)"</span><span style="color:#212121">)</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.6.4 运行方式</strong></h4><blockquote><p data-v-550653ec="" class="paragraph"><strong>文件位置</strong> : <code>application/huanhuan_web.py</code></p></blockquote><p data-v-550653ec="" class="paragraph"><strong>使用方法</strong> :</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 1. 确保Ollama服务运行</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> serve</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 2. 确保甄嬛模型已部署</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> list</span><span style="color:#D32F2F"> |</span><span style="color:#6F42C1"> grep</span><span style="color:#2B5581"> huanhuan</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 3. 启动Web应用</span></span>
<span class="line"><span style="color:#6F42C1">streamlit</span><span style="color:#2B5581"> run</span><span style="color:#2B5581"> application/huanhuan_web.py</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 4. 访问Web界面</span></span>
<span class="line"><span style="color:#C2C3C5"># 浏览器自动打开: http://localhost:8501</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><div data-v-4b915337="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 934px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/16ead059-0860-40c0-b1f9-357b8712ee17.png" alt="图片"></div><!----></div><p data-v-550653ec="" class="paragraph"><strong>文件结构</strong></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span>application/</span></span>
<span class="line"><span>├── huanhuan_web.py          # 主应用文件</span></span>
<span class="line"><span>├── chat_history/            # 对话历史存储目录</span></span>
<span class="line"><span>│   └── huanhuan_chat_*.json # 历史记录文件</span></span>
<span class="line"><span>└── __pycache__/            # Python缓存目录</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><h3 class="heading"><strong>4.7 agent应用-MCP最佳实践</strong></h3><div data-v-4d50615b="" class="attachment-block-container"><div data-v-4d50615b="" class="attachment-block-card-container"><div data-v-4d50615b="" class="attachment-block-card-icon"><svg data-v-4d50615b="" width="48" height="48" viewBox="0 0 32 32" class="attachment-block-card-icon-svg"><defs><path d="M1.5 0h14.086a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V26.5a1.5 1.5 0 01-1.5 1.5h-19A1.5 1.5 0 010 26.5v-25A1.5 1.5 0 011.5 0z" id="icon_file_js_svg__a"></path><path d="M16.293.293l5.414 5.414A1 1 0 0121.91 6H17.5A1.5 1.5 0 0116 4.5V.09a1 1 0 01.293.203z" id="icon_file_js_svg__b"></path></defs><g fill="none" fill-rule="evenodd"><g transform="translate(5 2)"><use fill="#eef" xlink:href="#icon_file_js_svg__a"></use><use fill="#ccc" xlink:href="#icon_file_js_svg__b"></use></g><g transform="translate(7 7) scale(0.019)"><path d="M420.693333 85.333333C353.28 85.333333 298.666667 139.946667 298.666667 207.36v71.68h183.04c16.64 0 30.293333 24.32 30.293333 40.96H207.36C139.946667 320 85.333333 374.613333 85.333333 442.026667v161.322666c0 67.413333 54.613333 122.026667 122.026667 122.026667h50.346667v-114.346667c0-67.413333 54.186667-122.026667 121.6-122.026666h224c67.413333 0 122.026667-54.229333 122.026666-121.642667V207.36C725.333333 139.946667 670.72 85.333333 603.306667 85.333333z m-30.72 68.693334c17.066667 0 30.72 5.12 30.72 30.293333s-13.653333 38.016-30.72 38.016c-16.64 0-30.293333-12.8-30.293333-37.973333s13.653333-30.336 30.293333-30.336z" fill="#3C78AA"></path><path d="M766.250667 298.666667v114.346666a121.6 121.6 0 0 1-121.6 121.984H420.693333A121.6 121.6 0 0 0 298.666667 656.597333v160a122.026667 122.026667 0 0 0 122.026666 122.026667h182.613334A122.026667 122.026667 0 0 0 725.333333 816.64v-71.68h-183.082666c-16.64 0-30.250667-24.32-30.250667-40.96h304.64A122.026667 122.026667 0 0 0 938.666667 581.973333v-161.28a122.026667 122.026667 0 0 0-122.026667-122.026666zM354.986667 491.221333l-0.170667 0.170667c0.512-0.085333 1.066667-0.042667 1.621333-0.170667z m279.04 310.442667c16.64 0 30.293333 12.8 30.293333 37.973333a30.293333 30.293333 0 0 1-30.293333 30.293334c-17.066667 0-30.72-5.12-30.72-30.293334s13.653333-37.973333 30.72-37.973333z" fill="#FDD835"></path></g></g></svg></div><div data-v-4d50615b="" class="attachment-block-card-info"><div data-v-4d50615b="" class="attachment-block-card-info-name">server.py</div><div data-v-4d50615b="" class="attachment-block-card-info-size">11.35KB</div></div><div data-v-4d50615b="" class="attachment-block-card-preview"><div data-v-4d50615b="" class="attachment-block-card-preview-wrapper"><svg data-v-4d50615b="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="VisibleOutlined" class="attachment-block-card-preview-btn"><path d="M11.985 18.5c3.238 0 6.236-2.06 9.015-6.513C18.292 7.55 15.3 5.5 11.985 5.5 8.67 5.5 5.689 7.549 3 11.987c2.76 4.454 5.748 6.513 8.985 6.513ZM1.502 12.89a1.782 1.782 0 0 1 .023-1.838C4.428 6.017 7.915 3.5 11.984 3.5c4.086 0 7.594 2.538 10.523 7.614l.028.048c.296.519.294 1.16-.01 1.675-3.006 5.108-6.52 7.663-10.541 7.663-4.007 0-7.501-2.537-10.482-7.61ZM12 16a4 4 0 1 1 0-8 4 4 0 0 1 0 8Zm0-2a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" fill="rgb(100, 106, 115)"></path></svg></div></div><div data-v-4d50615b="" class="attachment-block-card-menu" style="display: none;"><div data-v-583c080e="" data-v-4d50615b="" class="attachment-menu"><ul data-v-583c080e="" class="attachment-menu-group"><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="MagnifyLeftOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M9 2a1 1 0 0 1 0 2H5.414l5.293 5.293a1 1 0 0 1-1.414 1.414L4 5.414V9a1 1 0 0 1-2 0V3a1 1 0 0 1 1-1h6Zm6 20a1 1 0 1 1 0-2h3.586l-5.293-5.293a1 1 0 0 1 1.414-1.414L20 18.586V15a1 1 0 1 1 2 0v6a1 1 0 0 1-1 1h-6Z" fill="rgb(43, 47, 54)"></path></svg></li><li data-v-583c080e="" class="attachment-menu-item"><svg data-v-583c080e="" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-icon="DownloadOutlined" is-selected="false" class="attachment-menu-item-icon"><path d="M20 18a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-2a1 1 0 1 1 2 0v1h14v-1a1 1 0 0 1 1-1Zm-7-3.964 2.657-2.657a1 1 0 0 1 1.414 1.414c-1.414 1.415-2.828 2.83-4.244 4.244a1 1 0 0 1-1.412 0c-1.417-1.415-2.833-2.833-4.249-4.25a.993.993 0 0 1 .013-1.401.992.992 0 0 1 1.401-.013l2.42 2.42V3.5a1 1 0 1 1 2 0v10.536Z" fill="rgb(43, 47, 54)"></path></svg></li></ul></div></div></div></div><blockquote><p data-v-550653ec="" class="paragraph"><strong>实际文件位置</strong> : <code>mcp_server/server.py</code>undefined</p></blockquote><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.7.1 模块概述</strong></h4><p data-v-550653ec="" class="paragraph">MCP模块解决的问题是为甄嬛模型提供标准化的工具接口，让外部应用能够通过统一的协议与模型进行交互。通过标准化的MCP协议，将本地部署的甄嬛模型集成到Claude Desktop中，实现AI助手扩展。</p><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.7.2 实现流程</strong></h4><ol start="1"><li><p data-v-550653ec="" class="paragraph">首先，因为MCP服务器需要管理多个工具函数（状态）和处理请求（行为），所以我们通过FastMCP框架来定义服务器实例。</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">from</span><span style="color:#24292EFF"> mcp</span><span style="color:#212121">.</span><span style="color:#24292EFF">server</span><span style="color:#212121">.</span><span style="color:#24292EFF">fastmcp </span><span style="color:#D32F2F">import</span><span style="color:#24292EFF"> FastMCP</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 创建MCP服务器实例</span></span>
<span class="line"><span style="color:#24292EFF">mcp </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> FastMCP</span><span style="color:#212121">(</span><span style="color:#22863A">"huanhuan-chat"</span><span style="color:#212121">)</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><ol start="2"><li><p data-v-550653ec="" class="paragraph">考虑到不同环境下的配置需求，在模块初始化时设置环境配置，如果没有则使用默认值。</p></li></ol><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> get_ollama_host</span><span style="color:#24292EFF">() </span><span style="color:#212121">-&gt;</span><span style="color:#1976D2"> str</span><span style="color:#24292EFF">:</span></span>
<span class="line"><span style="color:#C2C3C5">    """Get the Ollama host from environment variables"""</span></span>
<span class="line"><span style="color:#D32F2F">    return</span><span style="color:#24292EFF"> os</span><span style="color:#212121">.</span><span style="color:#6F42C1">getenv</span><span style="color:#212121">(</span><span style="color:#22863A">"OLLAMA_HOST"</span><span style="color:#212121">, </span><span style="color:#22863A">"http://localhost:11434"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#D32F2F">def</span><span style="color:#6F42C1"> get_model_name</span><span style="color:#24292EFF">() </span><span style="color:#212121">-&gt;</span><span style="color:#1976D2"> str</span><span style="color:#24292EFF">:</span></span>
<span class="line"><span style="color:#C2C3C5">    """Get the model name from environment variables"""</span></span>
<span class="line"><span style="color:#D32F2F">    return</span><span style="color:#24292EFF"> os</span><span style="color:#212121">.</span><span style="color:#6F42C1">getenv</span><span style="color:#212121">(</span><span style="color:#22863A">"HUANHUAN_MODEL"</span><span style="color:#212121">, </span><span style="color:#22863A">"huanhuan_fast"</span><span style="color:#212121">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#24292EFF">OLLAMA_HOST </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> get_ollama_host</span><span style="color:#212121">()</span></span>
<span class="line"><span style="color:#24292EFF">MODEL_NAME </span><span style="color:#D32F2F">=</span><span style="color:#6F42C1"> get_model_name</span><span style="color:#212121">()</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><ol start="3"><li><p data-v-550653ec="" class="paragraph">定义工具函数，通过@mcp.tool()装饰器将普通函数转换为MCP工具。完成基础对话功能和对模型状态的获取</p></li><li><p data-v-550653ec="" class="paragraph">通过工具类扩展同一个模型的使用，比如角色扮演对话及诗词互动；</p></li></ol><p data-v-550653ec="" class="paragraph"></p><h4 class="heading"><strong>4.7.3 使用方式</strong></h4><p data-v-550653ec="" class="paragraph"><strong>启动方式</strong> :</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 方式1: 直接运行模块</span></span>
<span class="line"><span style="color:#6F42C1">python</span><span style="color:#2B5581"> -m</span><span style="color:#2B5581"> mcp_server</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 方式2: 运行主文件</span></span>
<span class="line"><span style="color:#6F42C1">python</span><span style="color:#2B5581"> mcp_server/server.py</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>Claude Desktop配置文件</strong> : <code>claude_desktop_config.json</code></p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#24292EFF">{</span></span>
<span class="line"><span style="color:#D32F2F">  "mcpServers"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> {</span></span>
<span class="line"><span style="color:#D32F2F">    "huanhuan-chat"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> {</span></span>
<span class="line"><span style="color:#D32F2F">      "command"</span><span style="color:#212121">:</span><span style="color:#22863A"> "python"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#D32F2F">      "args"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> [</span><span style="color:#22863A">"-m"</span><span style="color:#212121">,</span><span style="color:#22863A"> "mcp_server"</span><span style="color:#24292EFF">]</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#D32F2F">      "cwd"</span><span style="color:#212121">:</span><span style="color:#22863A"> "/Users/<USER>/Code/study/ollamaALL/ollama_文档"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#D32F2F">      "env"</span><span style="color:#212121">:</span><span style="color:#24292EFF"> {</span></span>
<span class="line"><span style="color:#D32F2F">        "OLLAMA_HOST"</span><span style="color:#212121">:</span><span style="color:#22863A"> "http://localhost:11434"</span><span style="color:#212121">,</span></span>
<span class="line"><span style="color:#D32F2F">        "HUANHUAN_MODEL"</span><span style="color:#212121">:</span><span style="color:#22863A"> "huanhuan_fast"</span></span>
<span class="line"><span style="color:#24292EFF">      }</span></span>
<span class="line"><span style="color:#24292EFF">    }</span></span>
<span class="line"><span style="color:#24292EFF">  }</span></span>
<span class="line"><span style="color:#24292EFF">}</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"><strong>使用方法</strong> :</p><div data-v-bcda4735="" class="code-block-wrapper"><div data-v-bcda4735="" class="code-block-container"><pre class="shiki min-light" style="background-color:#ffffff;color:#24292eff" tabindex="0"><code><span class="line"><span style="color:#C2C3C5"># 1. 启动Ollama服务</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> serve</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 2. 确保甄嬛模型可用</span></span>
<span class="line"><span style="color:#6F42C1">ollama</span><span style="color:#2B5581"> list</span><span style="color:#D32F2F"> |</span><span style="color:#6F42C1"> grep</span><span style="color:#2B5581"> huanhuan</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 3. 测试MCP服务器</span></span>
<span class="line"><span style="color:#6F42C1">python</span><span style="color:#2B5581"> -m</span><span style="color:#2B5581"> mcp_server</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C2C3C5"># 4. 在Claude Desktop中使用</span></span>
<span class="line"><span style="color:#C2C3C5"># 可用工具：</span></span>
<span class="line"><span style="color:#C2C3C5"># - chat_with_huanhuan: 基础对话（支持参数调节）</span></span>
<span class="line"><span style="color:#C2C3C5"># - get_model_info: 获取模型详细信息</span></span>
<span class="line"><span style="color:#C2C3C5"># - list_available_models: 列出所有可用模型</span></span>
<span class="line"><span style="color:#C2C3C5"># - check_ollama_status: 检查Ollama服务状态</span></span>
<span class="line"><span style="color:#C2C3C5"># - roleplay_conversation: 角色扮演对话（支持场景和情绪设定）</span></span>
<span class="line"><span style="color:#C2C3C5"># - poetry_interaction: 诗词创作和赏析</span></span></code></pre></div><button data-v-bcda4735="" class="copy-btn"><svg data-v-bcda4735="" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48h-48z" fill="none"></path><path d="M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z"></path></svg></button></div><p data-v-550653ec="" class="paragraph"></p><h2 class="heading">五、接下来、期待各位大佬大显神通啦~</h2><p data-v-550653ec="" class="paragraph">参考方案通过实现甄嬛角色AI助手，展示了端侧AI部署的完整技术方案。</p><p data-v-550653ec="" class="paragraph">从技术角度验证了小型语言模型在特定场景下的应用价值，为端侧AI的发展提供了有价值的参考。</p><p data-v-550653ec="" class="paragraph">同时，项目在保护用户隐私、降低使用成本、提升响应速度等方面体现了端侧AI的独特优势。</p><p data-v-550653ec="" class="paragraph"></p><p data-v-550653ec="" class="paragraph">各位大佬、会怎么来制作自己的端侧 MCP &amp; Agent 应用呢？</p><p data-v-550653ec="" class="paragraph"></p><p data-v-550653ec="" class="paragraph">这次赛事的链接是：<a target="https://startup.aliyun.com/aihackathon/mcp-agent" href="https://startup.aliyun.com/aihackathon/mcp-agent" class="" rel="noopener noreferrer nofollow">https://startup.aliyun.com/aihackathon/mcp-agent</a></p><p data-v-550653ec="" class="paragraph">核心赛题要求如下：</p><div data-v-582fded0="" class="dipwap-grid"><div data-v-582fded0="" class="dipwap-column" style="flex: 0.399202 1 0%;"><div data-v-4b915337="" data-v-582fded0="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 934px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/image.png" alt="赛题说明"></div><input data-v-4b915337="" type="text" class="image-caption" disabled="" value="赛题说明"></div></div><div data-v-582fded0="" class="dipwap-column" style="flex: 0.600798 1 0%;"><div data-v-4b915337="" data-v-582fded0="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 934px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/image(1).png" alt="评分维度"></div><input data-v-4b915337="" type="text" class="image-caption" disabled="" value="评分维度"></div><div data-v-4b915337="" data-v-582fded0="" class="image-container" style="align-items: center;"><div data-v-4b915337="" class="image-wrapper" style="width: 934px;"><img data-v-4b915337="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/image(2).png" alt="赛事奖励"></div><input data-v-4b915337="" type="text" class="image-caption" disabled="" value="赛事奖励"></div></div></div><p data-v-550653ec="" class="paragraph"></p><h2 class="heading">参考资料</h2><ol start="1"><li><p data-v-550653ec="" class="paragraph">chatHuanhuan：<a target="https://github.com/datawhalechina/self-llm/tree/master/examples/Chat-%E5%AC%9B%E5%AC%9B" href="https://github.com/datawhalechina/self-llm/tree/master/examples/Chat-%E5%AC%9B%E5%AC%9B" class="" rel="noopener noreferrer nofollow">https://github.com/datawhalechina/self-llm/tree/master/examples/Chat-%E5%AC%9B%E5%AC%9B</a></p></li><li><p data-v-550653ec="" class="paragraph">数据抽取：<a target="https://github.com/KMnO4-zx/extract-dialogue" href="https://github.com/KMnO4-zx/extract-dialogue" class="" rel="noopener noreferrer nofollow">https://github.com/KMnO4-zx/extract-dialogue</a></p></li></ol><p data-v-550653ec="" class="paragraph"></p></div></div><div data-v-74d9ef0f="" class="cutting-line"></div><div data-v-024ea750="" data-v-74d9ef0f="" class="DetailsComments-wrapper" loading="false"><div data-v-024ea750="" class="header"><div data-v-024ea750="" class="title">评论区</div></div><div data-v-024ea750="" class="comments-list"><!----><div data-v-024ea750="" class="comment-empty"><img data-v-024ea750="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/discuss-comment-empty-DQCmK43o.jpg" alt="commentEmpty"></div><div data-v-024ea750="" class="last-element"></div></div><div data-v-024ea750=""><div data-v-09a37ab2="" data-v-024ea750="" class="detailscommentinput input-bottom"><div data-v-09a37ab2="" class="main"><div data-v-09a37ab2="" class="input-box-wrapper"><div data-v-09a37ab2="" class="input-box"><div data-v-09a37ab2="" class="van-cell van-field input" style="min-height: 35px;"><!----><!----><div class="van-cell__value van-field__value"><div class="van-field__body"><textarea id="van-field-1-input" rows="1" class="van-field__control" placeholder="  请畅所欲言～" data-allow-mismatch="attribute" style="height: 35px;"></textarea><!----><!----><!----></div><!----><!----></div><!----><!----></div><img data-v-09a37ab2="" class="icon" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/commentIcon-CnuAlNjG.png" alt="commentInput"><!----></div><div data-v-09a37ab2="" class="input-slot"><div data-v-74d9ef0f="" class="comment-btn-area"><div data-v-74d9ef0f="" class="task-area-funcs-item"><div data-v-74d9ef0f="" class="task-area-funcs-item-icon task-area-funcs-item-icon-grey"><img data-v-74d9ef0f="" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/cert-grey-_Wo9NLs7.png"></div></div><!----><div data-v-74d9ef0f="" class="header-right-task-wrapper"><button data-v-74d9ef0f="" type="button" class="van-button van-button--primary van-button--normal header-right-task-btn" style="color: white; background: rgb(0, 82, 217); border-color: rgb(0, 82, 217);"><div class="van-button__content"><!----><span class="van-button__text"><span data-v-74d9ef0f="" class="header-right-task-btn-text">打卡</span><!----></span><!----></div></button><!----></div></div></div></div></div></div></div></div><div data-v-74d9ef0f="" class="top-button"><img data-v-1a7a7952="" data-v-74d9ef0f="" src="data:image/svg+xml,%3c?xml%20version=%271.0%27%20encoding=%27UTF-8%27?%3e%3csvg%20width=%2728px%27%20height=%2728px%27%20viewBox=%270%200%2028%2028%27%20version=%271.1%27%20xmlns=%27http://www.w3.org/2000/svg%27%20xmlns:xlink=%27http://www.w3.org/1999/xlink%27%3e%3ctitle%3etop%3c/title%3e%3cg%20id=%27%E9%A6%96%E9%A1%B5_v1.3%27%20stroke=%27none%27%20stroke-width=%271%27%20fill=%27none%27%20fill-rule=%27evenodd%27%3e%3cg%20id=%27home_%E4%BA%8C%E6%AC%A1%E5%85%83%E9%A3%8E_B%E6%9C%AA%E7%99%BB%E5%BD%95%27%20transform=%27translate(-1367.000000,%20-577.000000)%27%20fill=%27%23111111%27%20fill-rule=%27nonzero%27%3e%3cg%20id=%27%E7%BC%96%E7%BB%84-6%27%20transform=%27translate(1358.000000,%20568.000000)%27%3e%3cg%20id=%27%E5%BD%A2%E7%8A%B6%27%20transform=%27translate(9.000000,%209.000000)%27%3e%3cpath%20d=%27M2,2%20L26,2%20L26,4.8%20L2,4.8%20L2,2%20Z%20M20.884,13.5%20L15.3684,7.6832%20C15.0262,7.333%2014.543,7.1054%2014,7.1054%20C13.457,7.1054%2012.974,7.333%2012.6374,7.6868%20L7.1102,13.4958%20C6.8218,13.809%206.638,14.2136%206.638,14.6622%20C6.638,15.6252%207.4624,16.406%208.4786,16.406%20C8.4928,16.406%208.5066,16.4042%208.5206,16.4038%20L8.5206,16.4148%20L11,16.4148%20L11,26%20L17,26%20L17,16.4148%20L19.4122,16.4148%20L19.4122,16.4008%20C19.4486,16.4028%2019.4844,16.4064%2019.5216,16.4064%20C20.538,16.4064%2021.3622,15.6254%2021.3622,14.6622%20C21.3622,14.2136%2021.1782,13.8092%2020.884,13.5%20Z%27%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e" alt="top" class="draggable-icon" style="position: absolute; left: 0px; top: 0px;"></div></div><!----><!----><!----></div></div><!----></div><!----><!----></div>
    <script src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/jweixin-1.6.0.js.下载"></script>
  

<!----><!----><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/a2835f59-b016-4bfb-a6b6-eb46e2162351.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/05782c31-bfdb-426f-ba93-0c74f345b358.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/4ea6c200-224a-42bb-b2ad-08f55f2fe023.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/7891db64-c031-4c44-9208-6bcc03e2b18d.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/4e50697c-50c0-4eac-88ca-b7b25ee03f5f.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/1336696f-1dbb-45ce-b635-50742ead9d8c.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/886309e0-b468-469a-bf58-41629cc284b6.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/52bdac9c-4122-441d-88b2-98c5ee485e28.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/1046f6a4-7fd3-43ef-a39b-dbdbbdf0cacd.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/e30fb887-a3a3-4a97-9f7e-abc8a54b071b.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/5af02237-1622-44b0-b049-24e1d1afa367.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/e8144e3c-dab8-4d8c-b800-7a4359fa2be3.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/7c60d266-d86c-4f14-8d7d-96d67beb5232.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/dba7a7a3-9736-45e3-9ff9-e5be4a7d1666.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/ea3a4b28-9186-4f72-8140-cedb4b94628c.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/d3644ac1-de43-4e2c-add2-ed3b6eaba50e.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/3153e4db-b884-4090-81c5-248f7c2b1e3f.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/54d753b7-1598-4347-b3ea-ea5ecf5bbfb7.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/e98f2daf-de75-4517-9928-853ca15ac57e.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/5f2ecdac-de2c-457c-bcf5-86460b15a21e.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/8b0e388c-77b2-4e9f-adf2-857e04de990a.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/62eef7f4-8f5e-494c-ba0a-d62d4a5ff2f9.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/1f152c40-4bd5-471d-81b5-04456abbc20b.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/22edc2fe-3b6d-41ad-838e-d00dac876c87.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/4561f02b-cb7f-4986-951f-6bc0b53054a2.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/bf522e30-ecda-49e4-885c-092913fd2dac.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/1a824fab-4cac-4539-a0b2-f09f8db0e870.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/b3f8f30f-4f6f-477d-b5e6-187a211719a5.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/85efba76-1f11-4618-8357-363bda921dc0.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/16ead059-0860-40c0-b1f9-357b8712ee17.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/image.png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/image(1).png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div><div data-v-4b915337="" class="modal-mask" style="display: none;"><div data-v-4b915337="" class="modal-close">×</div><img data-v-4b915337="" class="modal-body" src="./魔搭Agent赛事（Agent端侧开发） _ Datawhale_files/image(2).png" width="0" height="0" style="transform: translate(0px, 0px);"><div data-v-4b915337="" class="modal-footer"><div data-v-4b915337="" class="zoom-out">-</div><div data-v-4b915337="" class="percentage">100%</div><div data-v-4b915337="" class="zoom-in">+</div></div></div></body></html>